<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
		PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
		"http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
	<properties resource="mybatis-generator.properties" />

	<classPathEntry location="${classPath}" />
	<context id="AutoGenerateTables" targetRuntime="MyBatis3">
		<property name="beginningDelimiter" value="" />
		<property name="endingDelimiter" value="" />
		<!-- 分页插件配置 -->
		<plugin type="com.estone.common.mybatis.MySQLLimitPlugin" />
		<commentGenerator>
			<!-- 是否去除自动生成的注释 true：是 ： false:否 -->
			<property name="suppressAllComments" value="true" />
			<!-- 是否生成字段备注 -->
			<property name="addRemarkComments" value="true"/>
		</commentGenerator>

		<!--数据库连接的信息：驱动类、连接地址、用户名、密码 -->
		<jdbcConnection driverClass="${jdbc_driver}" connectionURL="${jdbc_url}" userId="${jdbc_user}"
						password="${jdbc_password}">
		</jdbcConnection>
		<!-- 默认false，把JDBC DECIMAL 和 NUMERIC 类型解析为 Integer true，把JDBC DECIMAL 和 NUMERIC 类型解析为java.math.BigDecimal -->
		<javaTypeResolver>
			<property name="forceBigDecimals" value="true" />
		</javaTypeResolver>

		<!-- javaModelGenerator:model文件成配置 -->
		<javaModelGenerator targetPackage="${packageModel}" targetProject="${projectModel}">
			<!-- enableSubPackages:是否让schema作为包的后缀 -->
			<property name="enableSubPackages" value="true" />
			<!-- 从数据库返回的值被清理前后的空格 -->
			<property name="trimStrings" value="true" />
		</javaModelGenerator>
		<!-- sqlMapGenerator:map文件配置 -->
		<sqlMapGenerator targetPackage="${packageMapper}" targetProject="${projectMapper}">
			<property name="enableSubPackages" value="true" />
		</sqlMapGenerator>
		<!-- javaClientGenerator:dao层配置 -->
		<javaClientGenerator type="XMLMAPPER" targetPackage="${packageDao}" targetProject="${projectDao}">
			<property name="enableSubPackages" value="true" />
		</javaClientGenerator>
		<!-- tableName:用于自动生成代码的数据库表；domainObjectName:对应于数据库表的javaBean类名 -->
		<!-- enableCountByExample:设置数据库的数据比较的函数 enableUpdateByExample：设置数据库的数据更新的函数 enableDeleteByExample：设置数据库的数据删除的函数 enableSelectByExample：设置数据库的数据查询的函数
			selectByExampleQueryId：设置数据库的数据主键查询的函数 -->
		<table tableName="${tableName}" domainObjectName="${domainObjectName}" enableCountByExample="true"
			   enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="${pkColumn}" sqlStatement="${sqlStatement}" identity="${identity}" />
		</table>
	</context>
</generatorConfiguration>