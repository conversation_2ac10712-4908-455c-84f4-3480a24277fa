<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.erp_hr.biz.attendance.mapper.EmpLeaveBalanceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.estone.erp.erp_hr.biz.attendance.entity.EmpLeaveBalance">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="employee_name" property="employeeName" />
        <result column="employee_id" property="employeeId" />
        <result column="department" property="department" />
        <result column="leave_code" property="leaveCode" />
        <result column="leave_type" property="leaveType" />
        <result column="quota_cycle" property="quotaCycle" />
        <result column="quota_id" property="quotaId" />
        <result column="quota_num_per_hour" property="quotaNumPerHour" />
        <result column="quota_num_per_day" property="quotaNumPerDay" />
        <result column="used_num_per_hour" property="usedNumPerHour" />
        <result column="used_num_per_day" property="usedNumPerDay" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="total_hours" property="totalHours" />
        <result column="earliest_expiry_date" property="earliestExpiryDate" />
        <result column="status" property="status" />
        <result column="last_updated" property="lastUpdated" />
        <result column="calc_start_time" property="calcStartTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, employee_name, employee_id, department, leave_code, leave_type, quota_cycle, quota_id, quota_num_per_hour, quota_num_per_day, used_num_per_hour, used_num_per_day, start_time, end_time, total_hours, earliest_expiry_date, status, last_updated, calc_start_time
    </sql>

</mapper>
