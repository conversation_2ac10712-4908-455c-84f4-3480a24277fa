<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.erp_hr.biz.attendance.mapper.EmpLeaveTransactionRecordsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.estone.erp.erp_hr.biz.attendance.entity.EmpLeaveTransactionRecords">
        <id column="id" property="id" />
        <result column="record_id" property="recordId" />
        <result column="user_id" property="userId" />
        <result column="leave_code" property="leaveCode" />
        <result column="quota_id" property="quotaId" />
        <result column="cal_type" property="calType" />
        <result column="leave_record_type" property="leaveRecordType" />
        <result column="transaction_type" property="transactionType" />
        <result column="adjust_reason" property="adjustReason" />
        <result column="leave_status" property="leaveStatus" />
        <result column="status" property="status" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="used_num_per_hour" property="usedNumPerHour" />
        <result column="surplus_num_per_hour" property="surplusNumPerHour" />
        <result column="record_num_per_day" property="recordNumPerDay" />
        <result column="record_num_per_hour" property="recordNumPerHour" />
        <result column="calculated_hours" property="calculatedHours" />
        <result column="expire_date" property="expireDate" />
        <result column="leave_view_unit" property="leaveViewUnit" />
        <result column="leave_reason" property="leaveReason" />
        <result column="op_user_id" property="opUserId" />
        <result column="operator_name" property="operatorName" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="remark" property="remark" />
        <result column="calculated_hours" property="calculatedHours" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, record_id, user_id, leave_code, quota_id, cal_type, leave_record_type, transaction_type, adjust_reason, leave_status, status, start_time, end_time, gmt_create, gmt_modified, record_num_per_day, record_num_per_hour, used_num_per_hour, surplus_num_per_hour, leave_view_unit, leave_reason, op_user_id, operator_name, created_at, updated_at, remark, calculated_hours, expire_date
    </sql>

</mapper>
