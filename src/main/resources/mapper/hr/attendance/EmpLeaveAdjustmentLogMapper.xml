<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.erp_hr.biz.attendance.mapper.EmpLeaveAdjustmentLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.estone.erp.erp_hr.biz.attendance.entity.EmpLeaveAdjustmentLog">
        <id column="id" property="id" />
        <result column="log_time" property="logTime" />
        <result column="employee_id" property="employeeId" />
        <result column="employee_name" property="employeeName" />
        <result column="department" property="department" />
        <result column="batch_no" property="batchNo" />
        <result column="adjust_type" property="adjustType" />
        <result column="adjust_hours" property="adjustHours" />
        <result column="adjust_action" property="adjustAction" />
        <result column="original_issue_date" property="originalIssueDate" />
        <result column="original_hours" property="originalHours" />
        <result column="original_expiry_date" property="originalExpiryDate" />
        <result column="before_hours" property="beforeHours" />
        <result column="after_hours" property="afterHours" />
        <result column="operator_id" property="operatorId" />
        <result column="operator_name" property="operatorName" />
        <result column="operator_type" property="operatorType" />
        <result column="adjust_reason" property="adjustReason" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, log_time, employee_id, employee_name, department, batch_no, adjust_type, adjust_hours, adjust_action, original_issue_date, original_hours, original_expiry_date, before_hours, after_hours, operator_id, operator_name, operator_type, adjust_reason, remark, create_time, update_time
    </sql>

</mapper>
