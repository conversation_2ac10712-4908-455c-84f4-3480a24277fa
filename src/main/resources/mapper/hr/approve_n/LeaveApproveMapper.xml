<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.erp_hr.biz.approve_n.mapper.LeaveApproveMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.estone.erp.erp_hr.biz.approve_n.entity.LeaveApprove">
        <id column="id" property="id" />
        <result column="employee_no" property="employeeNo" />
        <result column="employee_name" property="employeeName" />
        <result column="dept_id" property="deptId" />
        <result column="position_id" property="positionId" />
        <result column="superior" property="superior" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="duration" property="duration" />
        <result column="leave_type" property="leaveType" />
        <result column="leave_reason" property="leaveReason" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="attachment" property="attachment" />
        <result column="status" property="status" />
        <result column="create_date" property="createDate" />
        <result column="complete_date" property="completeDate" />
        <result column="approver" property="approver" />
        <result column="updator" property="updator" />
        <result column="updated_date" property="updatedDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, employee_no, employee_name, dept_id, position_id, superior, start_date, end_date, duration, leave_type, leave_reason, process_instance_id, attachment, status, create_date, complete_date, approver, updator, updated_date
    </sql>

    <select id="listApplicationId" resultType="java.lang.Long">
        SELECT DISTINCT application_id FROM approval_process
        WHERE  application_type = 35 AND process_type !=1
        <if test="permissionEmployeeNos">
            AND approver in
            <foreach collection="permissionEmployeeNos" item="approver" index="index" open="(" close=")" separator=",">
                #{approver}
            </foreach>
        </if>
    </select>

</mapper>
