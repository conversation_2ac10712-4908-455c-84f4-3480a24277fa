<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.estone.erp.erp_hr.biz.approve_n.mapper.ApprovalProcessMapper">

    <resultMap type="com.estone.erp.erp_hr.biz.approve_n.entity.ApprovalProcessEntity" id="approvalProcessMap">
        <result property="id" column="id"/>
        <result property="processType" column="process_type"/>
        <result property="applicationType" column="application_type"/>
        <result property="applicationId" column="application_id"/>
        <result property="applicant" column="applicant"/>
        <result property="applicationDate" column="application_date"/>
        <result property="applicationContent" column="application_content"/>
        <result property="approver" column="approver"/>
        <result property="orderNumber" column="order_number"/>
        <result property="processStep" column="process_step"/>
        <result property="status" column="status"/>
        <result property="processContent" column="process_content"/>
        <result property="processDate" column="process_date"/>
        <result property="createdDate" column="created_date"/>
        <result property="creator" column="creator"/>
        <result property="updatedDate" column="updated_date"/>
        <result property="updator" column="updator"/>
    </resultMap>
    <sql id="Base_Column_List">
		a.id,
		a.process_type,
		a.application_type,
		a.application_id,
		a.applicant,
		a.application_date,
		a.application_content,
		a.approver,
		a.order_number,
		a.process_step,
		a.status,
		a.process_content,
		a.process_date,
		a.creator,
		a.updator,
		a.created_date,
		a.updated_date,
		(SELECT name FROM empl WHERE a.creator = employee_no) AS creatorName
	</sql>

    <select id="countByDTO" resultType="java.lang.Integer">
        SELECT COUNT(a.id)
        FROM approval_process a
        <include refid="ApprovalProcessCondition"/>
    </select>

    <select id="listApprovalProcess" resultMap="approvalProcessMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM approval_process a
        <include refid="ApprovalProcessCondition"/>
        <if test="orderByClause != null">
            ORDER BY ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                LIMIT ${offset}, ${limit}
            </if>
            <if test="offset == null">
                LIMIT ${limit}
            </if>
        </if>
    </select>

    <sql id="ApprovalProcessCondition">
        <where>
            <if test="dto.id">
                AND a.id = '${dto.id}'
            </if>
            <if test="dto.processType != null">
                AND a.process_type = '${dto.processType}'
            </if>
            <if test="dto.applicationType != null">
                AND a.application_type = '${dto.applicationType}'
            </if>
            <if test="dto.applicationId">
                AND a.application_id = '${dto.applicationId}'
            </if>
            <if test="dto.applicant != null and dto.applicant != ''">
                AND a.applicant = '${dto.applicant}'
            </if>

            <if test="dto.approver != null and dto.approver != ''">
                AND a.approver = '${dto.approver}'
            </if>
            <if test="dto.status != null">
                AND a.status = ${dto.status}
            </if>
            <if test="dto.statusList != null and dto.statusList.size !=0">
                AND a.status in
                <foreach collection="dto.statusList" item="status" index="index" open="("
                         close=")" separator=",">
                    ${status}
                </foreach>
            </if>
            <if test="dto.notExistsApplicationTypeList != null and dto.notExistsApplicationTypeList.size !=0">
                AND a.application_type NOT IN
                <foreach collection="dto.notExistsApplicationTypeList" item="applicationTypes" index="index" open="("
                         close=")" separator=",">
                    ${applicationTypes}
                </foreach>
            </if>

        </where>
    </sql>

</mapper>