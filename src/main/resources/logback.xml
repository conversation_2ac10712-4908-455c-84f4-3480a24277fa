<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">

	<include resource="org/springframework/boot/logging/logback/defaults.xml"/>
	<springProperty scope="context"
					name="springAppName"
					source="spring.application.name"/>

	<property name="LOG_FILE"
			  value="/var/log/zipkin/${springAppName}"/>

	<property name="log_path" value="/var/log" />

	<property name="CONSOLE_LOG_PATTERN"
			  value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr([${springAppName:-},%X{X-B3-TraceId:-},%X{X-B3-SpanId:-},%X{X-Span-Export:-}]){yellow} %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>

	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>%d{HH:mm:ss.SSS} [%t] %-5p [%C{1} %L] : %m%n</pattern>
		</encoder>
	</appender>

	<!--配置info日志级别单独文件-->
	<appender name="FILE-info" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>${log_path}/info/erp_hr_info_%d{yyyy-MM-dd}.log
			</FileNamePattern>
			<MaxHistory>30</MaxHistory>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>%d [%t] %p [%c %L] - &lt;%m&gt;%n</pattern>
		</encoder>
		<!--去掉日志文件大小控制，每日生成一份日志文件-->
		<!--<triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>128MB</MaxFileSize>
        </triggeringPolicy>-->
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>INFO</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>WARN</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>

	<!--配置debug日志级别单独文件-->
	<appender name="FILE-debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>${log_path}/debug/erp_hr_debug_%d{yyyy-MM-dd}.%i.log.zip
			</FileNamePattern>
			<MaxHistory>30</MaxHistory>
			<!-- 日志总保存量为10GB -->
			<totalSizeCap>10GB</totalSizeCap>
			<timeBasedFileNamingAndTriggeringPolicy
					class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<!--文件达到 最大1G时会被压缩和切割 -->
				<maxFileSize>1GB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>%d [%t] %p [%c %L] - &lt;%m&gt;%n</pattern>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>DEBUG</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>

	<!--配置error日志级别单独文件-->
	<appender name="FILE-error" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>${log_path}/error/erp_hr_error_%d{yyyy-MM-dd}.log
			</FileNamePattern>
			<MaxHistory>30</MaxHistory>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>%d [%t] %p [%c %L] - &lt;%m&gt;%n</pattern>
		</encoder>
		<!--<triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>128MB</MaxFileSize>
        </triggeringPolicy>-->
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>

	<!--配置warn日志级别单独文件-->
	<appender name="FILE-warn" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>${log_path}/warn/erp_hr_warn_%d{yyyy-MM-dd}.log
			</FileNamePattern>
			<MaxHistory>30</MaxHistory>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>%d [%t] %p [%c %L] - &lt;%m&gt;%n</pattern>
		</encoder>
		<!--<triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>128MB</MaxFileSize>
        </triggeringPolicy>-->
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>WARN</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>

	<!--线上改为 INFO-->
	<logger name="com.estone.erp" level="DEBUG" additivity="false">
		<appender-ref ref="FILE-info" />
		<appender-ref ref="FILE-debug" />
		<appender-ref ref="FILE-error" />
		<appender-ref ref="FILE-warn" />
		<appender-ref ref="STDOUT" />
	</logger>
	<logger name="org.springframework" level="WARN" additivity="false">
		<appender-ref ref="FILE-warn" />
		<appender-ref ref="FILE-info" />
		<appender-ref ref="FILE-debug" />
		<appender-ref ref="FILE-error" />
		<appender-ref ref="STDOUT" />
	</logger>
	<logger name="com.netflix.discovery.DiscoveryClient" level="WARN" additivity="false">
		<appender-ref ref="FILE-warn" />
		<appender-ref ref="FILE-info" />
		<appender-ref ref="FILE-debug" />
		<appender-ref ref="FILE-error" />
		<appender-ref ref="STDOUT" />
	</logger>

	<root level="INFO">
		<appender-ref ref="STDOUT" />
	</root>
</configuration>