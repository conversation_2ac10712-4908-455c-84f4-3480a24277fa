package com.estone.erp.erp_hr.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.dingtalkattendance_1_0.models.GetLeaveRecordsResponse;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.erp_hr.biz.approve_n.dto.AddApproveCommentDTO;
import com.estone.erp.erp_hr.biz.approve_n.dto.CanalApproveDTO;
import com.estone.erp.erp_hr.biz.approve_n.dto.ExecuteApproveDTO;
import com.estone.erp.erp_hr.biz.attendance.dto.GetColumnValDTO;
import com.estone.erp.erp_hr.biz.attendance.dto.ScheduleShiftListDTO;
import com.estone.erp.erp_hr.biz.attendance.entity.EmpLeaveTransactionRecords;
import com.estone.erp.erp_hr.biz.attendance.util.DateUtil;
import com.estone.erp.erp_hr.biz.dingding.api.DingDingCall;
import com.estone.erp.erp_hr.biz.dingding.api.DingDingConstant;
import com.estone.erp.erp_hr.biz.dingding.request.dto.DingAttendanceLeaveDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 钉钉接口工具类
 *
 * <AUTHOR>
 * @date 2021-06-24 10:57
 */
@Slf4j
public class DingDingClientUtils {

    private static DingDingCall dingDingCall;

    static {
        //从缓存获取钉钉接口Token
        dingDingCall = SpringUtils.getBean(DingDingCall.class);
    }

    /**
     * @Description 撤销审批
     * @Date 2022/7/18 15:37
     * @param: canalApproveDTO
     * @return com.dingtalk.api.response.OapiProcessInstanceTerminateResponse
     **/
    public static OapiProcessInstanceTerminateResponse canalApprove(CanalApproveDTO canalApproveDTO) throws Exception {
        if (canalApproveDTO == null || StrUtil.isBlank(canalApproveDTO.getProcessInstanceId())
                || (canalApproveDTO.getSystemFlag() == false && StrUtil.isBlank(canalApproveDTO.getOperatingUserid()))){
            throw new BusinessException("缺少必要的参数~");
        }

        //调用接口 直接抛出异常，该方法调用者捕获异常
        OapiProcessInstanceTerminateResponse response =
                dingDingCall.dingDIngApiCall(DingDingConstant.PROCESS_INSTANCE_TERMINATE, () ->{
                    OapiProcessInstanceTerminateRequest req = new OapiProcessInstanceTerminateRequest();
                    OapiProcessInstanceTerminateRequest.TerminateProcessInstanceRequestV2 processInstanceRequestV2 = new OapiProcessInstanceTerminateRequest.TerminateProcessInstanceRequestV2();
                    processInstanceRequestV2.setProcessInstanceId(canalApproveDTO.getProcessInstanceId());
                    /** 是否通过系统操作：true：由系统直接终止，false：由指定的操作者终止 */
                    processInstanceRequestV2.setIsSystem(canalApproveDTO.getSystemFlag());
                    processInstanceRequestV2.setRemark(canalApproveDTO.getRemark());
                    /** 操作人的userid。当is_system为false时，该参数必传。 */
                    processInstanceRequestV2.setOperatingUserid(canalApproveDTO.getOperatingUserid());
                    req.setRequest(processInstanceRequestV2);

                    return req;
                });

        return response;
    }

    /**
     * @Description 添加审批评论
     * @Date 2022/7/18 15:07
     * @param: addApproveCommentDTO
     * @return com.dingtalk.api.response.OapiProcessInstanceCommentAddResponse
     **/
    public static OapiProcessInstanceCommentAddResponse addApproveComment(AddApproveCommentDTO addApproveCommentDTO) throws Exception {
        if (addApproveCommentDTO == null
                || StrUtil.isBlank(addApproveCommentDTO.getProcessInstanceId())
                || StrUtil.isBlank(addApproveCommentDTO.getCommentUserId())
                || (StrUtil.isBlank(addApproveCommentDTO.getText()) && StrUtil.isBlank(addApproveCommentDTO.getUrl()))){
            throw new RuntimeException("缺少评论内容");
        }

        //调用接口 直接抛出异常，该方法调用者捕获异常
        OapiProcessInstanceCommentAddResponse response =
                dingDingCall.dingDIngApiCall(DingDingConstant.PROCESS_INSTANCE_COMMENT_ADD, () ->{
                    OapiProcessInstanceCommentAddRequest req = new OapiProcessInstanceCommentAddRequest();
                    OapiProcessInstanceCommentAddRequest.AddCommentRequest commentRequest = new OapiProcessInstanceCommentAddRequest.AddCommentRequest();
                    commentRequest.setProcessInstanceId(addApproveCommentDTO.getProcessInstanceId());
                    commentRequest.setCommentUserid(addApproveCommentDTO.getCommentUserId());
                    commentRequest.setText(addApproveCommentDTO.getProcessContentText());
/*                    OapiProcessInstanceCommentAddRequest.File file = new OapiProcessInstanceCommentAddRequest.File();
                    file.setPhotos(Arrays.asList("https://inews.gtimg.com/newsapp_bt/0/15085684727/1000"));
                    commentRequest.setFile(file);*/
                    req.setRequest(commentRequest);

                    return req;
                });

        return response;
    }

    /**
     * @Description 同意或拒绝审批任务
     * @Date 2022/7/18 14:52
     * @param: executeApproveDTO
     * @return com.dingtalk.api.response.OapiProcessListbyuseridResponse
     **/
    public static OapiProcessinstanceExecuteV2Response executeApproveDTO(ExecuteApproveDTO executeApproveDTO) throws Exception {
        if (executeApproveDTO == null
                || StrUtil.isBlank(executeApproveDTO.getProcessInstanceId())
                || StrUtil.isBlank(executeApproveDTO.getUserId())
                || StrUtil.isBlank(executeApproveDTO.getResult())
                || executeApproveDTO.getTaskId() == null){
            throw new BusinessException("缺少必要的参数~");
        }

        //调用接口 直接抛出异常，该方法调用者捕获异常
        OapiProcessinstanceExecuteV2Response response =
                dingDingCall.dingDIngApiCall(DingDingConstant.PROCESS_INSTANCE_EXECUTE, () ->{
                    OapiProcessinstanceExecuteV2Request req = new OapiProcessinstanceExecuteV2Request();
                    OapiProcessinstanceExecuteV2Request.ExecuteTaskRequest executeTaskRequest = new OapiProcessinstanceExecuteV2Request.ExecuteTaskRequest();
                    executeTaskRequest.setProcessInstanceId(executeApproveDTO.getProcessInstanceId());
                    executeTaskRequest.setActionerUserid(executeApproveDTO.getUserId());
                    executeTaskRequest.setTaskId(executeApproveDTO.getTaskId());
                    executeTaskRequest.setRemark(executeApproveDTO.getRemark());
                    executeTaskRequest.setResult(executeApproveDTO.getResult());
                    req.setRequest(executeTaskRequest);

                    return req;
                });

        return response;
    }

    /**
     * 调用钉钉接口获取打卡记录
     *
     * @param userId 员工钉钉id
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return java.util.List
     * <AUTHOR>
     * @date 2021/6/24 11:02
     */
    public static OapiAttendanceListResponse getAttendanceList(List<String> userIds, String startTime, String endTime) throws Exception {

        if (CollectionUtil.isEmpty(userIds)) {
            log.error("调用钉钉接口获取打卡记录--DingDingClientUtils.getAttendanceList 参数userIds为空，结束执行，返回结果为null");
            return null;
        }
        if (StrUtil.isBlank(startTime)) {
            log.error("调用钉钉接口获取打卡记录--DingDingClientUtils.getAttendanceList 参数startTime为空，结束执行，返回结果为null");
            return null;
        }
        if (StrUtil.isBlank(endTime)) {
            log.error("调用钉钉接口获取打卡记录--DingDingClientUtils.getAttendanceList 参数endTime为空，结束执行，返回结果为null");
            return null;
        }

        //调用接口 直接抛出异常，该方法调用者捕获异常
        OapiAttendanceListResponse response =
            dingDingCall.dingDIngApiCall(DingDingConstant.DING_DING_ATTENDANCE_LIST, () ->{
                OapiAttendanceListRequest request = new OapiAttendanceListRequest();
                //设置请求参数
                request.setUserIdList(userIds);
                request.setWorkDateFrom(startTime);
                request.setWorkDateTo(endTime);

                //钉钉默认只返回50条数据
                request.setOffset(DingDingConstant.DING_DEFAULT_RESPONSE_OFFSET_SIZE);
                request.setLimit(DingDingConstant.DING_DEFAULT_RESPONSE_LIMIT_SIZE);
                request.setIsI18n(false);
                return request;
            });

        return response;
    }

    /**
     * @Description 获取指定用户时间段内审批id
     * @Date 2021/11/19 17:18
     * @param: userId:用户id
     * @param: procCode：模板code
     * @param: startTime：开始时间戳
     * @param: endTime：结束时间戳
     * @param: cursor：游标
     * @return com.dingtalk.api.response.OapiProcessinstanceListidsResponse
     **/
    public static OapiProcessinstanceListidsResponse getUserProcInstIdList(String userId,String procCode,Long startTime,Long endTime,Long cursor) throws Exception {

        if (StrUtil.isBlank(userId)) {
            log.error("调用钉钉接口获取打卡记录--DingDingClientUtils.getAttendanceList 参数userId为空，结束执行，返回结果为null");
            return null;
        }

        if (StrUtil.isBlank(procCode)) {
            log.error("调用钉钉接口获取打卡记录--DingDingClientUtils.getAttendanceList 参数procCode为空，结束执行，返回结果为null");
            return null;
        }

        if (startTime == null) {
            log.error("调用钉钉接口获取打卡记录--DingDingClientUtils.getAttendanceList 参数startTime为空，结束执行，返回结果为null");
            return null;
        }

        if (endTime == null) {
            log.error("调用钉钉接口获取打卡记录--DingDingClientUtils.getAttendanceList 参数endTime为空，结束执行，返回结果为null");
            return null;
        }

        //调用接口 直接抛出异常，该方法调用者捕获异常
        OapiProcessinstanceListidsResponse response =
                dingDingCall.dingDIngApiCall(DingDingConstant.PROCESSINSTANCE_LISTIDS, () ->{
                    OapiProcessinstanceListidsRequest req = new OapiProcessinstanceListidsRequest();
                    req.setProcessCode(procCode);
                    req.setStartTime(startTime);
                    req.setEndTime(endTime);
                    req.setSize(20L);
                    req.setCursor(cursor);
                    req.setUseridList(userId);
                    return req;
                });

        return response;
    }

    /**
     * @Description  :获取指定用户可见的审批表单列表
     * @Date 2021/11/19 16:53
     * @param: userId：钉钉id
     * @Param: offset:偏移量
     * @return com.dingtalk.api.response.OapiProcessListbyuseridResponse
     **/
    public static OapiProcessListbyuseridResponse getUserProcessList(String userId,Long offset) throws Exception {

        if (StrUtil.isBlank(userId)) {
            log.error("调用钉钉接口获取打卡记录--DingDingClientUtils.getAttendanceList 参数userId为空，结束执行，返回结果为null");
            return null;
        }

        //调用接口 直接抛出异常，该方法调用者捕获异常
        OapiProcessListbyuseridResponse response =
                dingDingCall.dingDIngApiCall(DingDingConstant.PROCESS_LISTBYUSERID, () ->{
                    OapiProcessListbyuseridRequest req = new OapiProcessListbyuseridRequest();
                    req.setUserid(userId);
                    req.setOffset(offset);
                    req.setSize(100L);

                    return req;
                });

        return response;
    }

    /**
     * 获取审批实例详情
     * @param processInstanceId 审批实例id
     * @return com.dingtalk.api.response.OapiProcessinstanceGetResponse
     * <AUTHOR>
     * @date 2021/6/24 16:42
     */
    public static OapiProcessinstanceGetResponse getProcessDetails(String  processInstanceId) throws Exception {

        if (StrUtil.isBlank(processInstanceId)) {
            log.error("获取审批实例详情--DingDingClientUtils.getProcessDetails 参数为空，结束执行，返回结果为null");
            return null;
        }

        //调用接口 直接抛出异常，该方法调用者捕获异常
        OapiProcessinstanceGetResponse response =
            dingDingCall.dingDIngApiCall(DingDingConstant.DING_DING_ATTENDANCE_TOPAPI_PROCESSINSTANCE_GET, () ->{
                OapiProcessinstanceGetRequest request = new OapiProcessinstanceGetRequest();
                request.setProcessInstanceId(processInstanceId);
                return request;
            });

        return response;
    }

    /**
     * 查询排班概要信息
     * @param params
     * @return com.dingtalk.api.response.OapiAttendanceScheduleShiftListbydaysResponse
     * <AUTHOR>
     * @date 2021/6/24 16:47
     */
    public static OapiAttendanceScheduleShiftListbydaysResponse getScheduleShiftListByDays(ScheduleShiftListDTO params) throws Exception {

        if (params == null) {
            log.error("查询排班概要信息--DingDingClientUtils.getScheduleShiftListByDays 参数params为null，结束执行，返回结果为null");
            return null;
        }

        String opUserId = params.getOpUserId();
        String userIds = params.getUserIds();
        Long fromDateTime = params.getFromDateTime();
        Long toDateTime = params.getToDateTime();

        if (StrUtil.isBlank(opUserId)) {
            log.error("查询排班概要信息--DingDingClientUtils.getScheduleShiftListByDays 参数opUserId为空，结束执行，返回结果为null");
            return null;
        }

        if (StrUtil.isBlank(userIds)) {
            log.error("查询排班概要信息--DingDingClientUtils.getScheduleShiftListByDays 参数userIds为空，结束执行，返回结果为null");
            return null;
        }

        if (fromDateTime == null) {
            log.error("查询排班概要信息--DingDingClientUtils.getScheduleShiftListByDays 参数fromDateTime为null，结束执行，返回结果为null");
            return null;
        }

        if (toDateTime == null) {
            log.error("查询排班概要信息--DingDingClientUtils.getScheduleShiftListByDays 参数toDateTime为null，结束执行，返回结果为null");
            return null;
        }

        //时间跨度不能超过7天
        if (toDateTime - fromDateTime > DateUtil.SEVEN_MILLISECOND) {
            log.error("查询排班概要信息--DingDingClientUtils.getScheduleShiftListByDays 时间跨度超过7天，结束执行，返回结果为null");
            return null;
        }

        //调用接口 直接抛出异常，该方法调用者捕获异常
        OapiAttendanceScheduleShiftListbydaysResponse response =
                dingDingCall.dingDIngApiCall(DingDingConstant.DING_DING_ATTENDANCE_TOPAPI_PROCESSINSTANCE_SCHEDULE_SHIFT_LISTBYDAYS, () ->{
                    OapiAttendanceScheduleShiftListbydaysRequest request = new OapiAttendanceScheduleShiftListbydaysRequest();

                    //设置请求参数
                    request.setOpUserId(opUserId);
                    request.setUserids(userIds);
                    request.setFromDateTime(fromDateTime);
                    request.setToDateTime(toDateTime);
                    return request;
                });

        return response;
    }

    /**
     * 获取报表列值
     *
     * @param params
     * @return com.dingtalk.api.response.OapiAttendanceGetcolumnvalResponse
     * <AUTHOR>
     * @date 2021/6/24 17:30
     */
    public static OapiAttendanceGetcolumnvalResponse getColumnVal(GetColumnValDTO params) throws Exception {

        if (params == null) {
            log.error("获取报表列值--DingDingClientUtils.getColumnVal 参数params为null，结束执行，返回结果为null");
            return null;
        }

        String userId = params.getUserId();
        String columnIdList = params.getColumnIdList();
        Date fromDate = params.getFromDate();
        Date toDate = params.getToDate();

        if (StrUtil.isBlank(userId)) {
            log.error("查询排班概要信息--DingDingClientUtils.getColumnVal 参数userId为空，结束执行，返回结果为null");
            return null;
        }

        if (StrUtil.isBlank(columnIdList)) {
            log.error("查询排班概要信息--DingDingClientUtils.getColumnVal 参数columnIdList为空，结束执行，返回结果为null");
            return null;
        }

        if (fromDate == null) {
            log.error("查询排班概要信息--DingDingClientUtils.getColumnVal 参数fromDate为null，结束执行，返回结果为null");
            return null;
        }

        if (toDate == null) {
            log.error("查询排班概要信息--DingDingClientUtils.getColumnVal 参数toDate为null，结束执行，返回结果为null");
            return null;
        }

        //结束时间减去开始时间必须在31天以内
        if (DateUtil.differenceMillisecond(toDate, fromDate) > DateUtil.THIRTY_ONE_MILLISECOND) {
            log.error("查询排班概要信息--DingDingClientUtils.getColumnVal 结束时间减去开始时间必须在31天以内，结束执行，返回结果为null");
            return null;
        }

        //调用接口 直接抛出异常，该方法调用者捕获异常
        OapiAttendanceGetcolumnvalResponse response =
            dingDingCall.dingDIngApiCall(DingDingConstant.DING_DING_ATTENDANCE_GETCOLUMNVAL, () ->{
                OapiAttendanceGetcolumnvalRequest request = new OapiAttendanceGetcolumnvalRequest();
                //设置请求参数
                request.setUserid(userId);
                request.setColumnIdList(columnIdList);
                request.setFromDate(fromDate);
                request.setToDate(toDate);
                return request;
            });
        return response;
    }

    /**
     * 获取用户考勤组
     * @param userId 用户id
     * @return com.dingtalk.api.response.OapiAttendanceGetusergroupResponse
     * <AUTHOR>
     * @date 2021/6/24 17:34
     */
    public static OapiAttendanceGetusergroupResponse getUserGroup(String userId) throws Exception {

        if (StrUtil.isBlank(userId)) {
            log.error("获取用户考勤组--DingDingClientUtils.getUserGroup 参数userId为空，结束执行，返回结果为null");
            return null;
        }

        //调用接口 直接抛出异常，该方法调用者捕获异常
        OapiAttendanceGetusergroupResponse response =
            dingDingCall.dingDIngApiCall(DingDingConstant.DING_DING_ATTENDANCE_TOPAPI_ATTEMDAMCE_GETUSERGROUP, () ->{
                OapiAttendanceGetusergroupRequest request = new OapiAttendanceGetusergroupRequest();

                request.setUserid(userId);
                return request;
            });
        return response;
    }

    /**
     * 获取所有的班次信息
     * @param opUserId 操作人的userId
     * @return com.dingtalk.api.response.OapiAttendanceShiftListResponse
     * <AUTHOR>
     * @date 2021/7/6 10:00
     */
    public static OapiAttendanceShiftListResponse getShiftList(String opUserId) throws Exception {

        if (StrUtil.isBlank(opUserId)) {
            log.error("获取用户考勤组--DingDingClientUtils.getUserGroup 参数userId为空，结束执行，返回结果为null");
            return null;
        }

        //调用接口 直接抛出异常，该方法调用者捕获异常
        OapiAttendanceShiftListResponse response =
                dingDingCall.dingDIngApiCall(DingDingConstant.DING_DING_ATTENDANCE_SHIFT_LIST, () ->{
                    OapiAttendanceShiftListRequest  request = new OapiAttendanceShiftListRequest ();
                    request.setCursor(0L);
                    request.setOpUserId(opUserId);
                    return request;
                });
        return response;
    }

    /**
     * 获取班次详情
     * @param opUserId 操作人的userId
     * @param shiftId 班次id
     * @return com.dingtalk.api.response.OapiAttendanceShiftQueryResponse
     * <AUTHOR>
     * @date 2021/7/6 10:10
     */
    public static OapiAttendanceShiftQueryResponse  getShiftQuery(String opUserId,Long shiftId) throws Exception {

        if (StrUtil.isBlank(opUserId)) {
            log.error("获取用户班次详情userId为空--DingDingClientUtils.getUserGroup 参数userId为空，结束执行，返回结果为null");
            return null;
        }

        //调用接口 直接抛出异常，该方法调用者捕获异常
        OapiAttendanceShiftQueryResponse  response =
                dingDingCall.dingDIngApiCall(DingDingConstant.DING_DING_ATTENDANCE_SHIFT_QUERY, () ->{
                    OapiAttendanceShiftQueryRequest request = new OapiAttendanceShiftQueryRequest();
                    request.setOpUserId(opUserId);
                    request.setShiftId(shiftId);
                    return request;
                });
        return response;
    }
    /**
     * 批量获取考勤组
     * @param offset 偏移量
     * @param pageSize 分页大小
     * @return com.dingtalk.api.response.OapiAttendanceGetsimplegroupsResponse
     * <AUTHOR>
     * @date 2021/7/6 10:10
     */
    public static OapiAttendanceGetsimplegroupsResponse getGesineGroups(Long offset, Long pageSize) throws Exception {

        //调用接口 直接抛出异常，该方法调用者捕获异常
        OapiAttendanceGetsimplegroupsResponse response =
                dingDingCall.dingDIngApiCall(DingDingConstant.DING_DING_ATTENDANCE_DING_GROUPS, () ->{
                    OapiAttendanceGetsimplegroupsRequest request = new OapiAttendanceGetsimplegroupsRequest();
                    request.setOffset(offset);
                    request.setSize(pageSize);
                    return request;
                });
        return response;
    }


    /**
     * 获取请假状态
     * @param dto
     * @return
     */
    public static OapiAttendanceGetleavestatusResponse syncAttendanceLeave(DingAttendanceLeaveDTO dto) throws Exception{
        OapiAttendanceGetleavestatusResponse rsp = dingDingCall.dingDIngApiCall(DingDingConstant.DING_DING_ATTENDANCE_LEAVESTATUS, () ->{
            OapiAttendanceGetleavestatusRequest req = new OapiAttendanceGetleavestatusRequest();
            req.setUseridList(dto.getDingUserIds());
            req.setStartTime(dto.getStartDate().getTime());
            req.setEndTime(dto.getEndDate().getTime());
            req.setOffset(dto.getOffset());
            req.setSize(dto.getSize());
            return req;
        });
        return rsp;
    }

    // 同步假期余额
    public static OapiAttendanceVacationQuotaListResponse getLeaveBalanceFromAPI(String userids,String leaveCode,long size) throws Exception {
        if (StrUtil.isBlank(userids)) {
            log.error("同步假期余额--DingDingClientUtils.getLeaveBalanceFromAPI 参数userids为空，结束执行，返回结果为null");
            return null;
        }
        //调用接口 直接抛出异常，该方法调用者捕获异常
        OapiAttendanceVacationQuotaListResponse response =
                dingDingCall.dingDIngApiCall(DingDingConstant.QUOTA_LIST_GET, () ->{
                    OapiAttendanceVacationQuotaListRequest req = new OapiAttendanceVacationQuotaListRequest();
                    req.setLeaveCode(leaveCode);
                    req.setOpUserid(DingDingConstant.SYSTEM_OP_USERID);
                    req.setUserids(userids);
                    req.setOffset(0L);
                    req.setSize(size);
                    return req;
                });
        return response;
    }

    // 同步假期变动记录
    public static GetLeaveRecordsResponse getLeaveRecordsWithOptions(String userids, String leaveCode, long pageNumber) throws Exception {
        if (StrUtil.isBlank(userids)) {
            log.error("同步假期余额--DingDingClientUtils.getLeaveBalanceFromAPI 参数userids为空，结束执行，返回结果为null");
            return null;
        }
        com.aliyun.dingtalkattendance_1_0.models.GetLeaveRecordsRequest getLeaveRecordsRequest = new com.aliyun.dingtalkattendance_1_0.models.GetLeaveRecordsRequest()
                .setOpUserId(DingDingConstant.SYSTEM_OP_USERID)
                .setLeaveCode(leaveCode)
                .setUserIds(java.util.Arrays.asList(
                        userids.split(",")
                ))
                .setPageNumber(pageNumber)
                .setPageSize(200);
        return dingDingCall.getLeaveRecordsWithOptions(getLeaveRecordsRequest,new com.aliyun.dingtalkattendance_1_0.models.GetLeaveRecordsHeaders(),new com.aliyun.teautil.models.RuntimeOptions());
    }

    // 更新假期余额
    public static OapiAttendanceVacationQuotaUpdateResponse updateLeaveBalanceFromAPI(String userids, String leaveCode, Long surplusNumPerHour,
                                                                                      EmpLeaveTransactionRecords empLeaveTransactionRecords) throws Exception {
        if (StrUtil.isBlank(userids)) {
            log.error("更新假期余额--DingDingClientUtils.getLeaveBalanceFromAPI 参数userids为空，结束执行，返回结果为null");
            return null;
        }
        //调用接口 直接抛出异常，该方法调用者捕获异常
        OapiAttendanceVacationQuotaUpdateResponse response = dingDingCall.dingDIngApiCall(DingDingConstant.QUOTA_UPDATE, () -> {
            OapiAttendanceVacationQuotaUpdateRequest req = new OapiAttendanceVacationQuotaUpdateRequest();
            List<OapiAttendanceVacationQuotaUpdateRequest.LeaveQuotas> list = new ArrayList();
            OapiAttendanceVacationQuotaUpdateRequest.LeaveQuotas leaveQuotas = new OapiAttendanceVacationQuotaUpdateRequest.LeaveQuotas();
            list.add(leaveQuotas);
            leaveQuotas.setUserid(userids);
            leaveQuotas.setEndTime(empLeaveTransactionRecords.getEndTime());
            leaveQuotas.setStartTime(empLeaveTransactionRecords.getStartTime());
            leaveQuotas.setLeaveCode(leaveCode);
            leaveQuotas.setReason("管理员清零批次:"+empLeaveTransactionRecords.getGmtCreate());
            leaveQuotas.setQuotaNumPerHour(surplusNumPerHour);
            req.setLeaveQuotas(list);
            req.setOpUserid(DingDingConstant.SYSTEM_OP_USERID);
            return req;
        });
        return response;
    }
}
