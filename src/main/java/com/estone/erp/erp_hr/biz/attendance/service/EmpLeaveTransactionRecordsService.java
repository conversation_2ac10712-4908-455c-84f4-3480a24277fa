package com.estone.erp.erp_hr.biz.attendance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.erp_hr.biz.attendance.entity.EmpLeaveTransactionRecords;
import com.estone.erp.erp_hr.biz.employee.entity.EmplEntity;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 假期消费记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
public interface EmpLeaveTransactionRecordsService extends IService<EmpLeaveTransactionRecords> {

     void syncLeaveTransactionRecords(String userids);

     void clearOperation(EmpLeaveTransactionRecords record);
}
