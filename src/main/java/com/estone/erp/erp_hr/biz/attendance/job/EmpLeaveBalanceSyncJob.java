package com.estone.erp.erp_hr.biz.attendance.job;

import com.estone.erp.erp_hr.biz.attendance.service.impl.EmpLeaveBalanceServiceImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * XXL-Job 执行器类，用于调用 EmpLeaveBalanceServiceImpl 的 syncAllActiveUsersLeaveBalance 方法
 */
@Component
public class EmpLeaveBalanceSyncJob {

    private static final Logger logger = LoggerFactory.getLogger(EmpLeaveBalanceSyncJob.class);

    @Resource
    private EmpLeaveBalanceServiceImpl empLeaveBalanceService;

    /**
     * XXL-Job 任务方法，调用 syncAllActiveUsersLeaveBalance 同步在职用户假期余额
     */
    @XxlJob("syncAllActiveUsersLeaveBalanceJob")
    public ReturnT<String> execute(String params) throws Exception {
        try {
            logger.info("开始执行同步在职用户假期余额任务");
            boolean result = empLeaveBalanceService.syncAllActiveUsersLeaveBalance(null);
            if (result) {
                XxlJobLogger.log("同步在职用户假期余额任务执行成功");
                logger.info("同步在职用户假期余额任务执行成功");
            } else {
                XxlJobLogger.log("同步在职用户假期余额任务执行失败");
                logger.error("同步在职用户假期余额任务执行失败");
            }
            boolean result2 = empLeaveBalanceService.syncAllEmpLeaveTransactionRecords(null);
            if (result2) {
                XxlJobLogger.log("同步在职用户假期余额变动任务执行成功");
                logger.info("同步在职用户假期余额任务执行成功");
            } else {
                XxlJobLogger.log("同步在职用户假期余额变动任务执行失败");
                logger.error("同步在职用户假期余额任务执行失败");
            }
            boolean result3 = empLeaveBalanceService.calculateAndUpdateSurplusHours(null);
            if (result3) {
                XxlJobLogger.log("计算并更新剩余假期数量任务执行成功");
                logger.info("计算并更新剩余假期数量任务执行成功");
            } else {
                XxlJobLogger.log("计算并更新剩余假期数量任务执行失败");
                logger.error("计算并更新剩余假期数量任务执行失败");
            }
        } catch (Exception e) {
            XxlJobLogger.log("同步在职用户假期余额任务执行异常: " + e.getMessage());
            logger.error("同步在职用户假期余额任务执行异常", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
