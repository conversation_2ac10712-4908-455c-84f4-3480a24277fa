package com.estone.erp.erp_hr.biz.approve_n.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dingtalk.api.response.OapiProcessinstanceExecuteV2Response;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.CErrorCode;
import com.estone.erp.common.redis.config.RedisClusterTemplate;
import com.estone.erp.common.redis.config.RedissonTemplate;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.erp_hr.biz.approve_n.dto.*;
import com.estone.erp.erp_hr.biz.approve_n.entity.ApplyApprovalDetail;
import com.estone.erp.erp_hr.biz.approve_n.entity.ApprovalProcessEntity;
import com.estone.erp.erp_hr.biz.approve_n.enums.*;
import com.estone.erp.erp_hr.biz.approve_n.mapper.ApplyApprovalDetailMapper;
import com.estone.erp.erp_hr.biz.approve_n.service.*;
import com.estone.erp.erp_hr.biz.approve_n.utils.BuildProcessUtil;
import com.estone.erp.erp_hr.biz.approve_n.utils.GetNodeUserUtil;
import com.estone.erp.erp_hr.biz.attendance.controller.ApproveProcessUtils;
import com.estone.erp.erp_hr.biz.attendance.service.AttendanceProcinstService;
import com.estone.erp.erp_hr.biz.attendance.service.SyncDingRecordClockService;
import com.estone.erp.erp_hr.biz.dingding.enums.DingEventEnum;
import com.estone.erp.erp_hr.biz.dingding.mq.DingMqSendUtils;
import com.estone.erp.erp_hr.biz.employee.entity.EmplEntity;
import com.estone.erp.erp_hr.biz.employee.enums.EmplStatusEnum;
import com.estone.erp.erp_hr.biz.employee.service.EmplService;
import com.estone.erp.erp_hr.biz.recruitment.service.RecruitmentNeedService;
import com.estone.erp.erp_hr.biz.sys.enums.DeptTypeEnum;
import com.estone.erp.erp_hr.biz.sys.enums.LogModuleEnum;
import com.estone.erp.erp_hr.biz.sys.enums.OperationTypeEnum;
import com.estone.erp.erp_hr.biz.sys.mq.SysMqSendUtils;
import com.estone.erp.erp_hr.biz.sys.service.DeptService;
import com.estone.erp.erp_hr.biz.sys.service.PositionService;
import com.estone.erp.erp_hr.biz.sys.utils.OperationLogUtils;
import com.estone.erp.erp_hr.biz.sys.vo.DeptVO;
import com.estone.erp.erp_hr.common.constant.CommonConstant;
import com.estone.erp.erp_hr.common.constant.SeaweedFileConstant;
import com.estone.erp.erp_hr.common.constant.UserConstant;
import com.estone.erp.erp_hr.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
@Service
@Slf4j
public class ApplyApprovalServiceImpl extends ServiceImpl<ApplyApprovalDetailMapper, ApplyApprovalDetail> implements ApplyApprovalService {

    @Resource
    private GetNodeUserUtil getNodeUserUtil;
    @Resource
    private ApproveProcessUtils approveProcessUtils;
    @Resource
    private ApprovalProcessService approvalProcessService;
    @Resource
    private ApplyApprovalDetailService applyApprovalDetailService;
    @Resource
    private DeptService deptService;
    @Resource
    private EmplService emplService;
    @Resource
    private PositionService positionService;
    @Resource
    private RegularApproveService regularApproveService;
    @Resource
    private ResignApproveService resignApproveService;
    @Resource
    private RedisClusterTemplate redisClusterTemplate;
    @Autowired
    private RedissonTemplate redissonTemplate;
    @Autowired
    private TransferApproveService transferApproveService;
    @Autowired
    private RecruitmentNeedService recruitmentNeedService;
    @Autowired
    private DingMqSendUtils dingMqSendUtils;
    @Autowired
    private AttendanceProcinstService attendanceProcinstService;
    @Resource
    private LeaveApproveService leaveApproveService;

    // 补卡申请
    @Override
    public void subApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        List<EmplEntity> emps = getNodeUserUtil.getEmps(longDeptVOMap, curEmp.getDeptId(), entityList);
        List<ApprovalProcessEntity> approvalProcessEntityList = new ArrayList<>();
        List<List<String>> approvalEmps = new ArrayList<>();
        //当前用户
        approvalEmps.add(HrUtils.splitList(curEmp.getEmployeeNo(),","));
        // 补卡直接上级批准
        EmplEntity emplEntity = emps.get(0);
        approvalEmps.add(HrUtils.splitList(emplEntity.getEmployeeNo(),","));
        // 总部
        approvalEmps.add(HrUtils.splitList(UserConstant.OXY_EMPL_NO,","));
        // 获取当地人事
        DeptVO deptVO = longDeptVOMap.get(curEmp.getDeptId());
        if (deptVO == null || deptVO.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门不存在！");
        // 非西丽总部
        if (!getNodeUserUtil.deptIsXiLi(curEmp.getDeptId(),UserConstant.XI_LI_DEPT_ID)) {
            EmplEntity emplByPositionNameAndCompanyId = getNodeUserUtil.getEmplByPositionNameAndCompanyId(deptVO.getCompanyId(), UserConstant.HR_POSITION_ID);
            approvalEmps.get(approvalEmps.size()-1).add(emplByPositionNameAndCompanyId.getEmployeeNo());
        }
        LinkedHashMap<List<String>,String> approveUserIdsMap = new LinkedHashMap<>();
        int i = 0;
        for(List<String> empNos:approvalEmps){
            if (empNos.size() == 1 && !StringUtils.equalsIgnoreCase(empNos.get(0),curEmp.getEmployeeNo())){
                approveUserIdsMap.put(empNos,ActionTypeEnum.TRANSFER.getCode());
            }else if (empNos.size() > 1){
                approveUserIdsMap.put(empNos,ActionTypeEnum.EXTENSION_REGULAR.getCode());
            }
            for(String empNo:empNos) {
                ApprovalProcessEntity leaderApprovalProcessEntity = buildApprovalProcess(applyApprovalDTO);
                if (!StringUtils.equalsIgnoreCase(empNos.get(0),curEmp.getEmployeeNo())) {
                    leaderApprovalProcessEntity.setApprover(empNo);
                }
                leaderApprovalProcessEntity.setOrderNumber(++i);
                approvalProcessEntityList.add(leaderApprovalProcessEntity);
            }
        }
        ApplyApprovalDetail applyApprovalDetail = buildApprovalProcessDetail(applyApprovalDTO, curEmp);
        // 保存
        applyApprovalDTO.setApproveUserIds(approveUserIdsMap);
        approvalProcessService.create(applyApprovalDTO,approvalProcessEntityList,applyApprovalDetail);

    }

    @Override
    public ApplyApprovalDetail detail(Long id) {
        ApplyApprovalDetail detail = applyApprovalDetailService.getById(id);
        if (detail == null) {
            return null;
        }
        List<ApprovalProcessEntity> processEntityList = approvalProcessService.list(new QueryWrapper<ApprovalProcessEntity>()
                .eq("application_id", id).eq("application_type", detail.getApplicationType()));
        Asserts.isFalse(CollectionUtils.isEmpty(processEntityList), CErrorCode.REQUEST_PARAM_ERROR, "审批流数据缺失");
        processEntityList.sort(new Comparator<ApprovalProcessEntity>() {
            @Override
            public int compare(ApprovalProcessEntity o1, ApprovalProcessEntity o2) {
                return o1.getOrderNumber().compareTo(o2.getOrderNumber());
            }
        });
        detail.setApprovalProcessEntityList(processEntityList);
        detail.setDataJsonDto(JSONObject.parseArray(detail.getDataJson(),ApproveFormWorkSchemaInfoDTO.class));
        detail.setDataJson(null);
        return detail;
    }
    // 光明报废
    @Override
    public void scrapApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        DeptVO curDept = longDeptVOMap.get(curEmp.getDeptId());
        if (curDept == null || curDept.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门公司为空！");
        //获取部门员工
        Map<Long, List<EmplEntity>> empByDeptName = getNodeUserUtil.getEmpByDeptId(UserConstant.GM_PUR_DEPT_ID);
        List<EmplEntity> allDeptEmpl = empByDeptName.get(curDept.getCompanyId());
        if(allDeptEmpl.stream().allMatch(a -> !StringUtils.equalsIgnoreCase(curEmp.getEmployeeNo(),a.getEmployeeNo())))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非光明采购部员工！");

        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();
        Map<Long, String> selectPositionID2Map = positionService.selectPositionID2Map();
        //当前用户
        //approvalEmps.add(genEmpl(curEmp));
        // 抄送自己
        List<String> ccUserIds = new ArrayList<>();
        ccUserIds.add(curEmp.getUserId());
        //采购主管
        EmplEntity cgEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.GM_PUR_SUP_POSITION_ID,selectPositionID2Map);
        if (cgEmpl != null && !isCurEmpl(cgEmpl,curEmp))
            approvalEmps.add(genEmpl(cgEmpl));
        //采购经理
        EmplEntity gmEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.GM_PUR_GM_POSITION_ID,selectPositionID2Map);
        if (gmEmpl != null && !isCurEmpl(gmEmpl,curEmp))
            approvalEmps.add(genEmpl(gmEmpl));
        //仓库经理
        List<EmplEntity> wmsGmEmpls = getNodeUserUtil.matchEmps(entityList, UserConstant.WMS_PUR_GM_POSITION_ID,selectPositionID2Map);
        if (CollectionUtils.isNotEmpty(wmsGmEmpls)) {
            List<EmplEntity> collect = wmsGmEmpls.stream().filter(w -> !StringUtils.equalsIgnoreCase(w.getEmployeeNo(), curEmp.getEmployeeNo()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(collect))
            approvalEmps.add(genEmpl(collect));
        }
        //财务经理
        EmplEntity fmisEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.FMIS_POSITION_ID,selectPositionID2Map);
        if (fmisEmpl != null && !isCurEmpl(fmisEmpl,curEmp))
            approvalEmps.add(genEmpl((fmisEmpl)));
        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);
    }

    // 物品报废流程
    @Override
    public void otherScrapApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        List<EmplEntity> emps = getNodeUserUtil.getEmps(longDeptVOMap, curEmp.getDeptId(), entityList);

        //审批人
        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();
        Map<Long, String> selectPositionID2Map = positionService.selectPositionID2Map();
        // 上级批准
        if (CollectionUtils.isNotEmpty(emps)){
            for(EmplEntity e:emps){
                if(!isCurEmpl(e,curEmp))
                approvalEmps.add(genEmpl(e));
            }
        }
        // 抄送
        List<String> ccUserIds = new ArrayList<>();
        //行政主管
        EmplEntity xzEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.XZ_POSITION_ID,selectPositionID2Map);
        // 是否it部门物品
        if (StringUtils.equalsIgnoreCase("是",getNoteVal(applyApprovalDTO.getNodes(),"IT相关物品"))){
            //运维组长和技术总监审批
            EmplEntity ywEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.YW_POSITION_ID,selectPositionID2Map);
            if (ywEmpl != null && !isCurEmpl(ywEmpl,curEmp))
                approvalEmps.add(genEmpl(ywEmpl));
            EmplEntity ctoEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.CTO_POSITION_ID,selectPositionID2Map);
            if (ctoEmpl != null && !isCurEmpl(ctoEmpl,curEmp))
                approvalEmps.add(genEmpl(ctoEmpl));
            // 抄送应付组长
            EmplEntity yfEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.YF_POSITION_ID,selectPositionID2Map);
            if (yfEmpl != null)
                ccUserIds.add(yfEmpl.getUserId());
        }else{
            if (xzEmpl != null)
                ccUserIds.add(xzEmpl.getUserId());
        }

        if (xzEmpl != null && !isCurEmpl(xzEmpl,curEmp))
            approvalEmps.add(genEmpl(xzEmpl));
        //人事经理
        if (!StringUtils.equalsIgnoreCase(UserConstant.HR_LEADER_NO,curEmp.getEmployeeNo()))
            approvalEmps.add(genEmpl(UserConstant.HR_LEADER_NO,UserConstant.HR_LEADER_USER_ID));
        //财务
        EmplEntity fmisEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.FMIS_POSITION_ID,selectPositionID2Map);
        if (fmisEmpl != null && !isCurEmpl(fmisEmpl,curEmp))
            approvalEmps.add(genEmpl((fmisEmpl)));
        // 是否贵重
        if (StringUtils.equalsIgnoreCase("是",getNoteVal(applyApprovalDTO.getNodes(),"重要物品"))){
            //总经理
            approvalEmps.add(genEmpl(UserConstant.ZHOU_ZONG,UserConstant.ZHOU_ZONG_USER_ID));
        }
        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);
    }

    @Override
    public void amazonAwardApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        List<EmplEntity> emps = getNodeUserUtil.getEmps(longDeptVOMap, curEmp.getDeptId(), entityList);

        //审批人
        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();
        //是否部门负责人
        Boolean deptLeader = getNodeUserUtil.isDeptLeader(curEmp.getEmployeeNo());
        if(!deptLeader){
            // 上级批准,部门负责人不需要
            if (CollectionUtils.isNotEmpty(emps)){
                for(EmplEntity e:emps){
                    if(!isCurEmpl(e,curEmp))
                        approvalEmps.add(genEmpl(e));
                }
            }
        }
        //人事经理
        if (!StringUtils.equalsIgnoreCase(UserConstant.HR_LEADER_NO,curEmp.getEmployeeNo()))
            approvalEmps.add(genEmpl(UserConstant.HR_LEADER_NO,UserConstant.HR_LEADER_USER_ID));

        // 抄送
        List<String> ccUserIds = new ArrayList<>();
        //抄送HRBP和薪酬绩效组长
        ccUserIds.add(UserConstant.ADAMAZON_LEADER_USER_ID);
        ccUserIds.add(UserConstant.PERFORMANCE_LEADER_USER_ID);

        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);
    }

    @Override
    public void warehousingExpensesApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        DeptVO curDept = longDeptVOMap.get(curEmp.getDeptId());
        if (curDept == null || curDept.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门公司为空！");

        //审批人
        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();
        //人事经理
        if (!StringUtils.equalsIgnoreCase(UserConstant.HR_LEADER_NO,curEmp.getEmployeeNo()))
            approvalEmps.add(genEmpl(UserConstant.HR_LEADER_NO,UserConstant.HR_LEADER_USER_ID));
        //总经理
        if (!StringUtils.equalsIgnoreCase(UserConstant.ZHOU_ZONG,curEmp.getEmployeeNo()))
            approvalEmps.add(genEmpl(UserConstant.ZHOU_ZONG,UserConstant.ZHOU_ZONG_USER_ID));
        // 抄送
        List<String> ccUserIds = new ArrayList<>();

        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);
    }

    @Override
    public void stampBorrowingApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        DeptVO curDept = longDeptVOMap.get(curEmp.getDeptId());
        if (curDept == null || curDept.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门公司为空！");
/*        Boolean deptLeader = getNodeUserUtil.isDeptLeader(curEmp.getEmployeeNo());
        if(!deptLeader){
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非部门负责人！");
        }*/

        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();
        Map<Long, String> selectPositionID2Map = positionService.selectPositionID2Map();
        List<EmplEntity> emps = getNodeUserUtil.getEmps(longDeptVOMap, curEmp.getDeptId(), entityList);

        //是否部门负责人
        Boolean deptLeader = getNodeUserUtil.isDeptLeader(curEmp.getEmployeeNo());
        if(!deptLeader){
            //最后一个是部门负责人，添加抄送
            approvalEmps.add(genEmpl((emps.get(emps.size() - 1))));
        }
        // 抄送
        List<String> ccUserIds = new ArrayList<>();

        //财务经理
        EmplEntity fmisEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.FMIS_POSITION_ID,selectPositionID2Map);
        if (fmisEmpl != null && !isCurEmpl(fmisEmpl,curEmp))
            approvalEmps.add(genEmpl((fmisEmpl)));

        List<ApproveFormWorkSchemaInfoDTO> nodes = applyApprovalDTO.getNodes();
        //印章类型
        String typeName = nodes.stream().filter(item -> "印章类型".equals(item.getName())).findFirst().get().getValue().toString();
        //印章公司名称
        String companyName = nodes.stream().filter(item -> "选择公司名称".equals(item.getName())).findFirst().get().getValue().toString();

        if (typeName.equals("公章")){

            if (companyName.contains("其它")){
                //（公章，选择其它公司）税务会计审批且抄送
                EmplEntity swkjEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.TAX_ACCOUNTING_POSITION_ID,selectPositionID2Map);
                if (swkjEmpl != null && !isCurEmpl(swkjEmpl,curEmp)){
                    ccUserIds.add(swkjEmpl.getUserId());
                    approvalEmps.add(genEmpl((swkjEmpl)));
                }
            }else{
                //(公章，对应公司名）税务组长审批且抄送
                EmplEntity swzzEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.TAX_GROUP_POSITION_ID,selectPositionID2Map);
                if (swzzEmpl != null && !isCurEmpl(swzzEmpl,curEmp)){
                    ccUserIds.add(swzzEmpl.getUserId());
                    approvalEmps.add(genEmpl((swzzEmpl)));
                }
            }

        }else if (typeName.equals("财务章")){
            //(财务章）应收组长审批且抄送
            EmplEntity yszzEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.RECEIVABLE_GROUP_POSITION_ID,selectPositionID2Map);
            if (yszzEmpl != null && !isCurEmpl(yszzEmpl,curEmp)){
                ccUserIds.add(yszzEmpl.getUserId());
                approvalEmps.add(genEmpl((yszzEmpl)));
            }
        }else if (typeName.equals("法人章")){
            //（法人章）业务助理审批且抄送
            EmplEntity ywzlEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.BA_POSITION_ID,selectPositionID2Map);
            if (ywzlEmpl != null && !isCurEmpl(ywzlEmpl,curEmp)){
                ccUserIds.add(ywzlEmpl.getUserId());
                approvalEmps.add(genEmpl((ywzlEmpl)));
            }
        }
        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);
    }

    @Override
    public void warehouseEquipmentApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        DeptVO curDept = longDeptVOMap.get(curEmp.getDeptId());
        if (curDept == null || curDept.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门公司为空！");
        Map<Long, String> selectPositionID2Map = positionService.selectPositionID2Map();
        //仓储部下的所有员工，职位包含组长
        /*String deptVal = redisClusterTemplate.hGet(ErpUsermgtNRedisConStant.GET_FULL_ORG_ID_BY_ORG_ID, String.class, String.valueOf(UserConstant.WD_DEPT_ID));
        List<EmplEntity> collect = entityList.stream().filter(item -> HrUtils.splitLongList(deptVal, ",").contains(item.getDeptId())).collect(Collectors.toList());
        Optional<EmplEntity> first = collect.stream().filter(item -> item.getEmployeeNo().equals(curEmp.getEmployeeNo())).findFirst();
        if (!first.isPresent()){
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非仓储部！");
        }
        String positionName = selectPositionID2Map.get(curEmp.getPositionId());
        if (StrUtil.isBlank(positionName) || !positionName.contains("组长")){
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非仓储部组长或职位为空！");
        }*/
        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();

        // 抄送
        List<String> ccUserIds = new ArrayList<>();

        //仓库经理
        EmplEntity wmsGmEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.WMS_PUR_GM_POSITION_ID,selectPositionID2Map);
        if (wmsGmEmpl != null && !isCurEmpl(wmsGmEmpl,curEmp))
            approvalEmps.add(genEmpl(wmsGmEmpl));

        //仓库高级经理
        EmplEntity wmsGmHightEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.WMS_PUR_GM_HIGHT_POSITION_ID,selectPositionID2Map);
        if (wmsGmHightEmpl != null && !isCurEmpl(wmsGmHightEmpl,curEmp))
            approvalEmps.add(genEmpl(wmsGmHightEmpl));

        //采购经理
        EmplEntity gmEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.GM_PUR_GM_POSITION_ID,selectPositionID2Map);
        if (gmEmpl != null && !isCurEmpl(gmEmpl,curEmp))
            approvalEmps.add(genEmpl(gmEmpl));

        //财务经理
        EmplEntity fmisEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.FMIS_POSITION_ID,selectPositionID2Map);
        if (fmisEmpl != null && !isCurEmpl(fmisEmpl,curEmp))
            approvalEmps.add(genEmpl((fmisEmpl)));

        List<ApproveFormWorkSchemaInfoDTO> nodes = applyApprovalDTO.getNodes();
        //总金额
        Double money = Double.valueOf(nodes.stream().filter(item -> "设备申购总金额（元）".equals(item.getName())).findFirst().get().getValue().toString());
        if (money > 5000d){
            //总经理
            if (!StringUtils.equalsIgnoreCase(UserConstant.ZHOU_ZONG,curEmp.getEmployeeNo()))
                approvalEmps.add(genEmpl(UserConstant.ZHOU_ZONG,UserConstant.ZHOU_ZONG_USER_ID));
        }
        //采购助理
        EmplEntity cgzlEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.PA_POSITION_ID,selectPositionID2Map);
        if (cgzlEmpl != null){
            ccUserIds.add(cgzlEmpl.getUserId());
        }
        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);
    }

    @Override
    public void warehouseGoodsRequisitionApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        DeptVO curDept = longDeptVOMap.get(curEmp.getDeptId());
        if (curDept == null || curDept.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门公司为空！");
        Map<Long, String> selectPositionID2Map = positionService.selectPositionID2Map();

        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();

        // 抄送
        List<String> ccUserIds = new ArrayList<>();

        //仓库经理
        EmplEntity wmsGmEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.WMS_PUR_GM_POSITION_ID,selectPositionID2Map);
        if (wmsGmEmpl != null && !isCurEmpl(wmsGmEmpl,curEmp))
            approvalEmps.add(genEmpl(wmsGmEmpl));

        //仓库高级经理
        EmplEntity wmsGmHightEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.WMS_PUR_GM_HIGHT_POSITION_ID,selectPositionID2Map);
        if (wmsGmHightEmpl != null && !isCurEmpl(wmsGmHightEmpl,curEmp))
            approvalEmps.add(genEmpl(wmsGmHightEmpl));

        //行政主管
        EmplEntity xzEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.XZ_POSITION_ID,selectPositionID2Map);
        if (xzEmpl != null && !isCurEmpl(xzEmpl,curEmp))
            approvalEmps.add(genEmpl(xzEmpl));

        //人事经理
        if (!StringUtils.equalsIgnoreCase(UserConstant.HR_LEADER_NO,curEmp.getEmployeeNo()))
            approvalEmps.add(genEmpl(UserConstant.HR_LEADER_NO,UserConstant.HR_LEADER_USER_ID));

        //财务经理
        EmplEntity fmisEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.FMIS_POSITION_ID,selectPositionID2Map);
        if (fmisEmpl != null && !isCurEmpl(fmisEmpl,curEmp))
            approvalEmps.add(genEmpl((fmisEmpl)));

        //总经理
        if (!StringUtils.equalsIgnoreCase(UserConstant.ZHOU_ZONG,curEmp.getEmployeeNo()))
            approvalEmps.add(genEmpl(UserConstant.ZHOU_ZONG,UserConstant.ZHOU_ZONG_USER_ID));

        // 抄送应付组长
        EmplEntity yfEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.YF_POSITION_ID,selectPositionID2Map);
        if (yfEmpl != null)
            ccUserIds.add(yfEmpl.getUserId());

        //抄送分公司人事
        Long deptTop = getNodeUserUtil.getDeptTopByDeptId(curEmp.getDeptId());
        String userIdHr = getNodeUserUtil.getEmployeeNoOrUserId(deptTop, false);
        if(StrUtil.isNotBlank(userIdHr)){
            ccUserIds.add(userIdHr);
        }
        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);
    }

    @Override
    public void creditCardApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        DeptVO curDept = longDeptVOMap.get(curEmp.getDeptId());
        if (curDept == null || curDept.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门公司为空！");
        Map<Long, String> selectPositionID2Map = positionService.selectPositionID2Map();

        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();

        // 抄送
        List<String> ccUserIds = new ArrayList<>();

        //财务经理
        EmplEntity fmisEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.FMIS_POSITION_ID,selectPositionID2Map);
        if (fmisEmpl != null && !isCurEmpl(fmisEmpl,curEmp))
            approvalEmps.add(genEmpl((fmisEmpl)));

        //抄送业务助理及业务助理组长
        //业务助理
        EmplEntity ywzl = getNodeUserUtil.matchEmp(entityList, UserConstant.BA_POSITION_ID,selectPositionID2Map);
        if (ywzl != null && !isCurEmpl(ywzl,curEmp))
            ccUserIds.add(ywzl.getUserId());
        //业务助理组长
        EmplEntity ywzlzz = getNodeUserUtil.matchEmp(entityList, UserConstant.ALB_POSITION_ID,selectPositionID2Map);
        if (ywzlzz != null && !isCurEmpl(ywzlzz,curEmp))
            ccUserIds.add(ywzlzz.getUserId());

        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);
    }

    @Override
    public void itGoodsPurchaseApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        DeptVO curDept = longDeptVOMap.get(curEmp.getDeptId());
        if (curDept == null || curDept.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门公司为空！");
        Map<Long, String> selectPositionID2Map = positionService.selectPositionID2Map();

        //审批人
        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();
        // 抄送
        List<String> ccUserIds = new ArrayList<>();

        //运维组长和技术总监审批
        EmplEntity ywEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.YW_POSITION_ID,selectPositionID2Map);
        if (ywEmpl != null && !isCurEmpl(ywEmpl,curEmp))
            approvalEmps.add(genEmpl(ywEmpl));
        EmplEntity ctoEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.CTO_POSITION_ID,selectPositionID2Map);
        if (ctoEmpl != null && !isCurEmpl(ctoEmpl,curEmp))
            approvalEmps.add(genEmpl(ctoEmpl));
        //财务经理
        EmplEntity fmisEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.FMIS_POSITION_ID,selectPositionID2Map);
        if (fmisEmpl != null && !isCurEmpl(fmisEmpl,curEmp))
            approvalEmps.add(genEmpl((fmisEmpl)));
        //总经理
        if (!StringUtils.equalsIgnoreCase(UserConstant.ZHOU_ZONG,curEmp.getEmployeeNo()))
            approvalEmps.add(genEmpl(UserConstant.ZHOU_ZONG,UserConstant.ZHOU_ZONG_USER_ID));
        //应付组长
        EmplEntity yfEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.YF_POSITION_ID,selectPositionID2Map);
        if (yfEmpl != null && !isCurEmpl(yfEmpl,curEmp))
            approvalEmps.add(genEmpl((yfEmpl)));
        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);

    }

    @Override
    public void notItGoodsPurchaseApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        DeptVO curDept = longDeptVOMap.get(curEmp.getDeptId());
        if (curDept == null || curDept.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门公司为空！");
        Map<Long, String> selectPositionID2Map = positionService.selectPositionID2Map();

        //审批人
        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();
        // 抄送
        List<String> ccUserIds = new ArrayList<>();

        //行政主管
        EmplEntity xzEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.XZ_POSITION_ID,selectPositionID2Map);
        if (xzEmpl != null && !isCurEmpl(xzEmpl,curEmp))
            approvalEmps.add(genEmpl(xzEmpl));
        //人事经理
        if (!StringUtils.equalsIgnoreCase(UserConstant.HR_LEADER_NO,curEmp.getEmployeeNo()))
            approvalEmps.add(genEmpl(UserConstant.HR_LEADER_NO,UserConstant.HR_LEADER_USER_ID));
        //财务经理
        EmplEntity fmisEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.FMIS_POSITION_ID,selectPositionID2Map);
        if (fmisEmpl != null && !isCurEmpl(fmisEmpl,curEmp))
            approvalEmps.add(genEmpl((fmisEmpl)));
        //总经理
        if (!StringUtils.equalsIgnoreCase(UserConstant.ZHOU_ZONG,curEmp.getEmployeeNo()))
            approvalEmps.add(genEmpl(UserConstant.ZHOU_ZONG,UserConstant.ZHOU_ZONG_USER_ID));
        //应付组长
        EmplEntity yfEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.YF_POSITION_ID,selectPositionID2Map);
        if (yfEmpl != null && !isCurEmpl(yfEmpl,curEmp))
            approvalEmps.add(genEmpl((yfEmpl)));
        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);

    }

    @Override
    public void administrativeMaterialApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        DeptVO curDept = longDeptVOMap.get(curEmp.getDeptId());
        if (curDept == null || curDept.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门公司为空！");
        Map<Long, String> selectPositionID2Map = positionService.selectPositionID2Map();

        //审批人
        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();
        // 抄送
        List<String> ccUserIds = new ArrayList<>();

        //行政主管
        EmplEntity xzEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.XZ_POSITION_ID,selectPositionID2Map);
        if (xzEmpl != null && !isCurEmpl(xzEmpl,curEmp))
            approvalEmps.add(genEmpl(xzEmpl));
        //人事经理
        if (!StringUtils.equalsIgnoreCase(UserConstant.HR_LEADER_NO,curEmp.getEmployeeNo()))
            approvalEmps.add(genEmpl(UserConstant.HR_LEADER_NO,UserConstant.HR_LEADER_USER_ID));
        //财务经理
        EmplEntity fmisEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.FMIS_POSITION_ID,selectPositionID2Map);
        if (fmisEmpl != null && !isCurEmpl(fmisEmpl,curEmp))
            approvalEmps.add(genEmpl((fmisEmpl)));
        //应付组长
        EmplEntity yfEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.YF_POSITION_ID,selectPositionID2Map);
        if (yfEmpl != null && !isCurEmpl(yfEmpl,curEmp))
            approvalEmps.add(genEmpl((yfEmpl)));
        //抄送欧晓怡
        ccUserIds.add(UserConstant.OXY_USER_ID);
        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);
    }

    @Override
    public void mrbsApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        DeptVO curDept = longDeptVOMap.get(curEmp.getDeptId());
        if (curDept == null || curDept.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门公司为空！");
        Map<Long, String> selectPositionID2Map = positionService.selectPositionID2Map();

        //审批人
        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();
        // 抄送
        List<String> ccUserIds = new ArrayList<>();

        //行政前台
        EmplEntity xzEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.EFD_POSITION_ID,selectPositionID2Map);
        if (xzEmpl != null && !isCurEmpl(xzEmpl,curEmp)){
            approvalEmps.add(genEmpl(xzEmpl));
            //抄送
            ccUserIds.add(xzEmpl.getUserId());
        }

        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);
    }

    @Override
    public void warehouseItemBorrowingApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        DeptVO curDept = longDeptVOMap.get(curEmp.getDeptId());
        if (curDept == null || curDept.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门公司为空！");
        Map<Long, String> selectPositionID2Map = positionService.selectPositionID2Map();

        //审批人
        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();
        // 抄送
        List<String> ccUserIds = new ArrayList<>();
        //申请信息
        List<ApproveFormWorkSchemaInfoDTO> nodes = applyApprovalDTO.getNodes();
        //申请部门
        String deptName = nodes.stream().filter(item -> "申请部门".equals(item.getName())).findFirst().get().getValue().toString();
        //申请人的上级部门
        DeptVO deptVO = getNodeUserUtil.getDeptByDeptIdAndType(curEmp.getDeptId(), DeptTypeEnum.DEPT.getCode());
        if (deptVO == null){
            throw new BusinessException("发起人上级类型为部门的组织架构为空");
        }
        if (!deptVO.getName().equalsIgnoreCase(deptName)){
            throw new BusinessException("发起人和申请部门不一致，发起人上级部门【"+deptVO.getName()+"】申请部门名称：【"+deptName+"】");
        }

        //产品开发经理
        EmplEntity cpkfjlEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.NPDP_POSITION_ID,selectPositionID2Map);
        if (cpkfjlEmpl != null && !isCurEmpl(cpkfjlEmpl,curEmp)){
            approvalEmps.add(genEmpl(cpkfjlEmpl));
        }
        //仓库高级经理
        EmplEntity wmsGmHightEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.WMS_PUR_GM_HIGHT_POSITION_ID,selectPositionID2Map);
        if (wmsGmHightEmpl != null && !isCurEmpl(wmsGmHightEmpl,curEmp)){
            approvalEmps.add(genEmpl(wmsGmHightEmpl));
        }
        //人事经理
        if (!StringUtils.equalsIgnoreCase(UserConstant.HR_LEADER_NO,curEmp.getEmployeeNo()))
            approvalEmps.add(genEmpl(UserConstant.HR_LEADER_NO,UserConstant.HR_LEADER_USER_ID));
        //财务经理
        EmplEntity fmisEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.FMIS_POSITION_ID,selectPositionID2Map);
        if (fmisEmpl != null && !isCurEmpl(fmisEmpl,curEmp))
            approvalEmps.add(genEmpl((fmisEmpl)));
        //是否重要物品
        String value = nodes.stream().filter(item -> "重要物品".equals(item.getName())).findFirst().get().getValue().toString();
        if ("是".equals(value)){
            //总经理
            if (!StringUtils.equalsIgnoreCase(UserConstant.ZHOU_ZONG,curEmp.getEmployeeNo()))
                approvalEmps.add(genEmpl(UserConstant.ZHOU_ZONG,UserConstant.ZHOU_ZONG_USER_ID));
        }
        //抄送仓库经理
        List<EmplEntity> wmsGmEmpls = getNodeUserUtil.matchEmps(entityList, UserConstant.WMS_PUR_GM_POSITION_ID,selectPositionID2Map);
        if (CollectionUtils.isNotEmpty(wmsGmEmpls)) {
            List<EmplEntity> collect1 = wmsGmEmpls.stream().filter(w -> !StringUtils.equalsIgnoreCase(w.getEmployeeNo(), curEmp.getEmployeeNo()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(collect1))
                ccUserIds.addAll(collect1.stream().map(item ->item.getUserId()).collect(Collectors.toList()));
        }
        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);
    }

    @Override
    public void ystdSubscribeApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        DeptVO curDept = longDeptVOMap.get(curEmp.getDeptId());
        if (curDept == null || curDept.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门公司为空！");
        Map<Long, String> selectPositionID2Map = positionService.selectPositionID2Map();

        //审批人
        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();
        // 抄送
        List<String> ccUserIds = new ArrayList<>();
        //仓库经理
        List<EmplEntity> wmsGmEmpls = getNodeUserUtil.matchEmps(entityList, UserConstant.WMS_PUR_GM_POSITION_ID,selectPositionID2Map);
        if (CollectionUtils.isNotEmpty(wmsGmEmpls)) {
            List<EmplEntity> collect1 = wmsGmEmpls.stream().filter(w -> !StringUtils.equalsIgnoreCase(w.getEmployeeNo(), curEmp.getEmployeeNo()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(collect1))
                approvalEmps.add(genEmpl(collect1));
        }
        //仓库高级经理
        EmplEntity wmsGmHightEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.WMS_PUR_GM_HIGHT_POSITION_ID,selectPositionID2Map);
        if (wmsGmHightEmpl != null && !isCurEmpl(wmsGmHightEmpl,curEmp))
            approvalEmps.add(genEmpl((wmsGmHightEmpl)));
        //应付组长
        EmplEntity yfEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.YF_POSITION_ID,selectPositionID2Map);
        if (yfEmpl != null && !isCurEmpl(yfEmpl,curEmp))
            approvalEmps.add(genEmpl((yfEmpl)));
        //财务经理
        EmplEntity fmisEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.FMIS_POSITION_ID,selectPositionID2Map);
        if (fmisEmpl != null && !isCurEmpl(fmisEmpl,curEmp))
            approvalEmps.add(genEmpl((fmisEmpl)));
        //采购主管
        EmplEntity cgzgEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.GM_PUR_SUP_POSITION_ID,selectPositionID2Map);
        if (cgzgEmpl != null && !isCurEmpl(cgzgEmpl,curEmp))
            approvalEmps.add(genEmpl((cgzgEmpl)));
        //采购经理
        EmplEntity cgjlEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.GM_PUR_GM_POSITION_ID,selectPositionID2Map);
        if (cgjlEmpl != null && !isCurEmpl(cgjlEmpl,curEmp))
            approvalEmps.add(genEmpl((cgjlEmpl)));
        //采购专员
        EmplEntity cgzyEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.BUYER_POSITION_ID,selectPositionID2Map);
        if (cgzyEmpl != null && !isCurEmpl(cgzyEmpl,curEmp))
            approvalEmps.add(genEmpl((cgzyEmpl)));

        //抄送自己
        ccUserIds.add(curEmp.getUserId());
        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);
    }

    @Override
    public void bearFeesApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        DeptVO curDept = longDeptVOMap.get(curEmp.getDeptId());
        if (curDept == null || curDept.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门公司为空！");
        Map<Long, String> selectPositionID2Map = positionService.selectPositionID2Map();

        //审批人
        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();
        // 抄送
        List<String> ccUserIds = new ArrayList<>();
        //采购主管
        EmplEntity cgzgEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.GM_PUR_SUP_POSITION_ID,selectPositionID2Map);
        if (cgzgEmpl != null && !isCurEmpl(cgzgEmpl,curEmp))
            approvalEmps.add(genEmpl((cgzgEmpl)));
        //采购经理
        EmplEntity cgjlEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.GM_PUR_GM_POSITION_ID,selectPositionID2Map);
        if (cgjlEmpl != null && !isCurEmpl(cgjlEmpl,curEmp))
            approvalEmps.add(genEmpl((cgjlEmpl)));
        //仓库经理
        List<EmplEntity> wmsGmEmpls = getNodeUserUtil.matchEmps(entityList, UserConstant.WMS_PUR_GM_POSITION_ID,selectPositionID2Map);
        if (CollectionUtils.isNotEmpty(wmsGmEmpls)) {
            List<EmplEntity> collect1 = wmsGmEmpls.stream().filter(w -> !StringUtils.equalsIgnoreCase(w.getEmployeeNo(), curEmp.getEmployeeNo()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(collect1))
                approvalEmps.add(genEmpl(collect1));
        }
        //财务经理
        EmplEntity fmisEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.FMIS_POSITION_ID,selectPositionID2Map);
        if (fmisEmpl != null && !isCurEmpl(fmisEmpl,curEmp))
            approvalEmps.add(genEmpl((fmisEmpl)));
        //人事经理
        if (!StringUtils.equalsIgnoreCase(UserConstant.HR_LEADER_NO,curEmp.getEmployeeNo()))
            approvalEmps.add(genEmpl(UserConstant.HR_LEADER_NO,UserConstant.HR_LEADER_USER_ID));

        //抄送应付组长
        EmplEntity yfEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.YF_POSITION_ID,selectPositionID2Map);
        if (yfEmpl != null && !isCurEmpl(yfEmpl,curEmp))
            ccUserIds.add(yfEmpl.getUserId());
        //抄送薪酬绩效组长
        EmplEntity xcjxzzEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.EP_GROUP_POSITION_ID,selectPositionID2Map);
        if (xcjxzzEmpl != null && !isCurEmpl(xcjxzzEmpl,curEmp))
            ccUserIds.add(xcjxzzEmpl.getUserId());
        //抄送薪酬绩效专员
        EmplEntity xcjxzyEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.EP_POSITION_ID,selectPositionID2Map);
        if (xcjxzyEmpl != null && !isCurEmpl(xcjxzyEmpl,curEmp))
            ccUserIds.add(xcjxzyEmpl.getUserId());
        //抄送自己
        ccUserIds.add(curEmp.getUserId());

        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);
    }

    @Override
    public void allEmplBearFeesApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        DeptVO curDept = longDeptVOMap.get(curEmp.getDeptId());
        if (curDept == null || curDept.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门公司为空！");
        Map<Long, String> selectPositionID2Map = positionService.selectPositionID2Map();
        List<EmplEntity> emps = getNodeUserUtil.getEmps(longDeptVOMap, curEmp.getDeptId(), entityList);
        //审批人
        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();
        // 抄送人
        List<String> ccUserIds = new ArrayList<>();
        //是否部门负责人
        Boolean deptLeader = getNodeUserUtil.isDeptLeader(curEmp.getEmployeeNo());
        if(!deptLeader){
            // 上级批准,部门负责人不需要
            if (CollectionUtils.isNotEmpty(emps)){
                for(EmplEntity e:emps){
                    if(!isCurEmpl(e,curEmp))
                        approvalEmps.add(genEmpl(e));
                }
            }
            //最后一个是部门负责人，添加抄送
            ccUserIds.add(emps.get(emps.size() - 1).getUserId());
        }

        //人事经理
        if (!StringUtils.equalsIgnoreCase(UserConstant.HR_LEADER_NO,curEmp.getEmployeeNo())){
            ccUserIds.add(UserConstant.HR_LEADER_USER_ID);
            approvalEmps.add(genEmpl(UserConstant.HR_LEADER_NO,UserConstant.HR_LEADER_USER_ID));
        }
        //财务经理
        EmplEntity fmisEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.FMIS_POSITION_ID,selectPositionID2Map);
        if (fmisEmpl != null && !isCurEmpl(fmisEmpl,curEmp))
            approvalEmps.add(genEmpl((fmisEmpl)));
        //周总
        approvalEmps.add(genEmpl(UserConstant.ZHOU_ZONG,UserConstant.ZHOU_ZONG_USER_ID));

        //抄送应付组长
        EmplEntity yfEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.YF_POSITION_ID,selectPositionID2Map);
        if (yfEmpl != null && !isCurEmpl(yfEmpl,curEmp))
            ccUserIds.add(yfEmpl.getUserId());
        //抄送薪酬绩效组长
        EmplEntity xcjxzzEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.EP_GROUP_POSITION_ID,selectPositionID2Map);
        if (xcjxzzEmpl != null && !isCurEmpl(xcjxzzEmpl,curEmp))
            ccUserIds.add(xcjxzzEmpl.getUserId());
        //抄送薪酬绩效专员
        EmplEntity xcjxzyEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.EP_POSITION_ID,selectPositionID2Map);
        if (xcjxzyEmpl != null && !isCurEmpl(xcjxzyEmpl,curEmp))
            ccUserIds.add(xcjxzyEmpl.getUserId());

        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);
    }

    @Override
    public void vehicleMaintenanceApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        DeptVO curDept = longDeptVOMap.get(curEmp.getDeptId());
        if (curDept == null || curDept.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门公司为空！");
        Map<Long, String> selectPositionID2Map = positionService.selectPositionID2Map();
        //审批人
        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();
        // 抄送人
        List<String> ccUserIds = new ArrayList<>();

        //仓库经理
        EmplEntity wmsGmEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.WMS_PUR_GM_POSITION_ID,selectPositionID2Map);
        if (wmsGmEmpl != null && !isCurEmpl(wmsGmEmpl,curEmp))
            approvalEmps.add(genEmpl(wmsGmEmpl));

        //仓库高级经理
        EmplEntity wmsGmHightEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.WMS_PUR_GM_HIGHT_POSITION_ID,selectPositionID2Map);
        if (wmsGmHightEmpl != null && !isCurEmpl(wmsGmHightEmpl,curEmp))
            approvalEmps.add(genEmpl(wmsGmHightEmpl));

        //采购经理
        EmplEntity gmEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.GM_PUR_GM_POSITION_ID,selectPositionID2Map);
        if (gmEmpl != null && !isCurEmpl(gmEmpl,curEmp))
            approvalEmps.add(genEmpl(gmEmpl));

        //财务经理
        EmplEntity fmisEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.FMIS_POSITION_ID,selectPositionID2Map);
        if (fmisEmpl != null && !isCurEmpl(fmisEmpl,curEmp))
            approvalEmps.add(genEmpl((fmisEmpl)));

        List<ApproveFormWorkSchemaInfoDTO> nodes = applyApprovalDTO.getNodes();
        //总金额
        Double money = Double.valueOf(nodes.stream().filter(item -> "预计维修费用（元）".equals(item.getName())).findFirst().get().getValue().toString());
        if (money >= 5000d){
            //总经理
            if (!StringUtils.equalsIgnoreCase(UserConstant.ZHOU_ZONG,curEmp.getEmployeeNo()))
                approvalEmps.add(genEmpl(UserConstant.ZHOU_ZONG,UserConstant.ZHOU_ZONG_USER_ID));
        }
        //采购助理抄送
        EmplEntity cgzlEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.PA_POSITION_ID,selectPositionID2Map);
        if (cgzlEmpl != null && !isCurEmpl(cgzlEmpl,curEmp)){
            ccUserIds.add(cgzlEmpl.getUserId());
        }

        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);
    }

    @Override
    public void warehouseDepartmentLogisticsMaintenanceApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        DeptVO curDept = longDeptVOMap.get(curEmp.getDeptId());
        if (curDept == null || curDept.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门公司为空！");
        Map<Long, String> selectPositionID2Map = positionService.selectPositionID2Map();
        //审批人
        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();
        // 抄送人
        List<String> ccUserIds = new ArrayList<>();

        //仓库经理
        EmplEntity wmsGmEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.WMS_PUR_GM_POSITION_ID,selectPositionID2Map);
        if (wmsGmEmpl != null && !isCurEmpl(wmsGmEmpl,curEmp))
            approvalEmps.add(genEmpl(wmsGmEmpl));

        //仓库高级经理
        EmplEntity wmsGmHightEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.WMS_PUR_GM_HIGHT_POSITION_ID,selectPositionID2Map);
        if (wmsGmHightEmpl != null && !isCurEmpl(wmsGmHightEmpl,curEmp))
            approvalEmps.add(genEmpl(wmsGmHightEmpl));

        //人事行政主管
        EmplEntity hrAdminEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.HR_ADMIN_POSITION_ID,selectPositionID2Map);
        if (hrAdminEmpl != null && !isCurEmpl(hrAdminEmpl,curEmp))
            approvalEmps.add(genEmpl(hrAdminEmpl));

        //安全主任
        EmplEntity soEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.SAFETY_OFFICER_POSITION_ID,selectPositionID2Map);
        if (soEmpl != null && !isCurEmpl(soEmpl,curEmp))
            approvalEmps.add(genEmpl(soEmpl));

        //西丽行政前台
        EmplEntity lxlEmpl = entityList.stream().filter(item -> item.getUserId().equals(UserConstant.OXY_USER_ID)).findFirst().get();
        if (lxlEmpl != null && !isCurEmpl(lxlEmpl,curEmp))
            approvalEmps.add(genEmpl(lxlEmpl));

        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);
    }

    @Override
    public void businessEntertainmentApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        DeptVO curDept = longDeptVOMap.get(curEmp.getDeptId());
        if (curDept == null || curDept.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门公司为空！");
        Map<Long, String> selectPositionID2Map = positionService.selectPositionID2Map();
        //审批人
        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();
        // 抄送人
        List<String> ccUserIds = new ArrayList<>();
        Boolean deptLeader = getNodeUserUtil.isDeptLeader(curEmp.getEmployeeNo());
        if (!deptLeader) {
            //部门负责人
            List<EmplEntity> emps = getNodeUserUtil.getEmps(longDeptVOMap, curEmp.getDeptId(), entityList);
            if (CollUtil.isNotEmpty(emps)){
                EmplEntity emplLeader = emps.get(emps.size() - 1);
                if (emplLeader != null && !isCurEmpl(emplLeader,curEmp)){
                    approvalEmps.add(genEmpl(emplLeader.getEmployeeNo(),emplLeader.getUserId()));
                }
            }
        }

        //总经理
        if (!StringUtils.equalsIgnoreCase(UserConstant.ZHOU_ZONG,curEmp.getEmployeeNo()))
            approvalEmps.add(genEmpl(UserConstant.ZHOU_ZONG,UserConstant.ZHOU_ZONG_USER_ID));
        //应付组长
        EmplEntity yfEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.YF_POSITION_ID,selectPositionID2Map);
        if (yfEmpl != null && !isCurEmpl(yfEmpl,curEmp))
            approvalEmps.add(genEmpl(yfEmpl.getEmployeeNo(),yfEmpl.getUserId()));

        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);
    }

    @Override
    public void dailyReimbursementApply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        DeptVO curDept = longDeptVOMap.get(curEmp.getDeptId());
        if (curDept == null || curDept.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门公司为空！");
        Map<Long, String> selectPositionID2Map = positionService.selectPositionID2Map();
        //审批人
        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();
        // 抄送人
        List<String> ccUserIds = new ArrayList<>();

        //申请信息
        List<ApproveFormWorkSchemaInfoDTO> nodes = applyApprovalDTO.getNodes();
        //申请部门
        String deptName = nodes.stream().filter(item -> "申请部门".equals(item.getName())).findFirst().get().getValue().toString();
        //申请人的上级部门
        DeptVO deptVO = getNodeUserUtil.getDeptByDeptIdAndType(curEmp.getDeptId(), DeptTypeEnum.DEPT.getCode());
        if (deptVO == null){
            throw new BusinessException("发起人上级类型为部门的组织架构为空");
        }
        if (!deptVO.getName().equalsIgnoreCase(deptName)){
            throw new BusinessException("发起人和申请部门不一致，发起人上级部门【"+deptVO.getName()+"】申请部门名称：【"+deptName+"】");
        }

        //部门负责人
        List<EmplEntity> emps = getNodeUserUtil.getEmps(longDeptVOMap, curEmp.getDeptId(), entityList);
        if (CollUtil.isNotEmpty(emps)){
            EmplEntity emplLeader = emps.get(emps.size() - 1);
            if (emplLeader != null && !isCurEmpl(emplLeader,curEmp)){
                approvalEmps.add(genEmpl(emplLeader.getEmployeeNo(),emplLeader.getUserId()));
            }
        }
        //应付组长
        EmplEntity yfEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.YF_POSITION_ID,selectPositionID2Map);
        if (yfEmpl != null && !isCurEmpl(yfEmpl,curEmp))
            approvalEmps.add(genEmpl(yfEmpl.getEmployeeNo(),yfEmpl.getUserId()));

        //财务经理
        EmplEntity fmisEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.FMIS_POSITION_ID,selectPositionID2Map);
        if (fmisEmpl != null && !isCurEmpl(fmisEmpl,curEmp))
            approvalEmps.add(genEmpl((fmisEmpl)));

        //总金额
        Double money = Double.valueOf(nodes.stream().filter(item -> "付款金额（元）".equals(item.getName())).findFirst().get().getValue().toString());
        if (money >= 5000d){
            //总经理
            if (!StringUtils.equalsIgnoreCase(UserConstant.ZHOU_ZONG,curEmp.getEmployeeNo()))
                approvalEmps.add(genEmpl(UserConstant.ZHOU_ZONG,UserConstant.ZHOU_ZONG_USER_ID));
        }
        //出纳
        List<EmplEntity> cashiers = getNodeUserUtil.matchEmps(entityList, UserConstant.CASHIER_POSITION_ID,selectPositionID2Map);
        if (CollUtil.isNotEmpty(cashiers)){
            approvalEmps.add(genEmpl(cashiers));
        }

        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);
    }

    @Override
    public void amazonVatapply(ApplyApprovalDTO applyApprovalDTO) throws Exception {
        Map<Long, DeptVO> longDeptVOMap = deptService.selectDeptID2Map();
        List<EmplEntity> entityList = emplService.list(new QueryWrapper<EmplEntity>().in("status", EmplStatusEnum.getAllNoDimissionStatus()));
        List<EmplEntity> curEmps = entityList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getEmployeeNo(), applyApprovalDTO.getEmployeeNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curEmps))
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 非在职状态！");
        EmplEntity curEmp = curEmps.get(0);
        if (curEmp.getDeptId() == null || curEmp.getPositionId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门/职位为空！");
        DeptVO curDept = longDeptVOMap.get(curEmp.getDeptId());
        if (curDept == null || curDept.getCompanyId() == null)
            throw new RuntimeException("员工 ["+applyApprovalDTO.getEmployeeNo()+"] 部门公司为空！");
        Map<Long, String> selectPositionID2Map = positionService.selectPositionID2Map();
        List<EmplEntity> emps = getNodeUserUtil.getEmps(longDeptVOMap, curEmp.getDeptId(), entityList);
        //审批人
        List<LinkedHashMap<String,String>> approvalEmps = new ArrayList<>();
        // 抄送人
        List<String> ccUserIds = new ArrayList<>();
        //是否部门负责人
        Boolean deptLeader = getNodeUserUtil.isDeptLeader(curEmp.getEmployeeNo());
        if(!deptLeader){
            // 上级批准,部门负责人不需要
            if (CollectionUtils.isNotEmpty(emps)){
                for(EmplEntity e:emps){
                    if(!isCurEmpl(e,curEmp))
                        approvalEmps.add(genEmpl(e));
                }
            }
        }

        //财务经理
        EmplEntity fmisEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.FMIS_POSITION_ID,selectPositionID2Map);
        if (fmisEmpl != null && !isCurEmpl(fmisEmpl,curEmp))
            approvalEmps.add(genEmpl((fmisEmpl)));

        //业务助理组长
        EmplEntity ywzlzz = getNodeUserUtil.matchEmp(entityList, UserConstant.ALB_POSITION_ID,selectPositionID2Map);
        if (ywzlzz != null && !isCurEmpl(ywzlzz,curEmp))
            approvalEmps.add(genEmpl((ywzlzz)));

        //销管组长
        EmplEntity xgzzEmpl = getNodeUserUtil.matchEmp(entityList, UserConstant.PP_GROUP_AMAZON_POSITION_ID,selectPositionID2Map);
        if (xgzzEmpl != null && !isCurEmpl(xgzzEmpl,curEmp))
            approvalEmps.add(genEmpl((xgzzEmpl)));

        createApprovalProcess(applyApprovalDTO,approvalEmps,ccUserIds,curEmp);
    }

    // 组装生成审批实例
    public void  createApprovalProcess(ApplyApprovalDTO applyApprovalDTO,List<LinkedHashMap<String,String>> approvalEmps,List<String> ccUserIds,EmplEntity curEmp ) throws Exception {
        List<ApprovalProcessEntity> approvalProcessEntityList = new ArrayList<>();
        LinkedHashMap<List<String>,String> approveUserIdsMap = new LinkedHashMap<>();
        ApprovalProcessEntity curApprovalProcessEntity = buildApprovalProcess(applyApprovalDTO);
        curApprovalProcessEntity.setStatus(ApproveStatusEnum.WAITTING.getCode());
        approvalProcessEntityList.add(curApprovalProcessEntity);
        int i = curApprovalProcessEntity.getOrderNumber();
        for(LinkedHashMap<String,String> empNos:approvalEmps){
            List<String> userIds = new ArrayList<>();
            int j = i++;
            for(String empNo:empNos.keySet()) {
                ApprovalProcessEntity leaderApprovalProcessEntity = buildApprovalProcess(applyApprovalDTO);
                if (StringUtils.isBlank(curApprovalProcessEntity.getApprover())){
                    curApprovalProcessEntity.setApprover(empNo);
                }
                // 第一条审批
                if (j == 1){
                    leaderApprovalProcessEntity.setStatus(ApproveStatusEnum.WAITTING.getCode());
                }else{
                    leaderApprovalProcessEntity.setStatus(ApproveStatusEnum.DEFAULT.getCode());
                }
                leaderApprovalProcessEntity.setApprover(empNo);
                leaderApprovalProcessEntity.setProcessType(ProcessTypeEnum.APPROVE.getCode());
                userIds.add(empNos.get(empNo));
                leaderApprovalProcessEntity.setOrderNumber(j+1);
                approvalProcessEntityList.add(leaderApprovalProcessEntity);
            }
            if (userIds.size() == 1){
                approveUserIdsMap.put(userIds,ActionTypeEnum.TRANSFER.getCode());
            }else if (userIds.size() > 1){
                approveUserIdsMap.put(userIds,ActionTypeEnum.EXTENSION_REGULAR.getCode());
            }
        }
        log.info("审批人："+JSONObject.toJSONString(approvalEmps));
        ApplyApprovalDetail applyApprovalDetail = buildApprovalProcessDetail(applyApprovalDTO, curEmp);
        // 保存
        applyApprovalDTO.setApproveUserIds(approveUserIdsMap);
        //抄送
        applyApprovalDTO.setCcUserIds(ccUserIds);
        approvalProcessService.create(applyApprovalDTO,approvalProcessEntityList,applyApprovalDetail);
    }

    // 是否当前用户
    public boolean isCurEmpl(EmplEntity empl,EmplEntity curEmpl){
        if (empl == null || curEmpl == null || StringUtils.isBlank(empl.getEmployeeNo())
                || StringUtils.isBlank(curEmpl.getEmployeeNo())) return false;
        if (StringUtils.equalsIgnoreCase(empl.getEmployeeNo(),curEmpl.getEmployeeNo())) return true;
        return false;
    }

    // 组装单签/或
    public LinkedHashMap<String, String> genEmpl(Object... empl){
        if (empl== null || empl.length == 0) return null;
        LinkedHashMap<String, String> empMap = new LinkedHashMap<>();
        for(Object num :empl){
            if (num instanceof String){
                empMap.put(empl[0].toString(),empl[1].toString());
                return empMap;
            }
            empMap.put(((EmplEntity)num).getEmployeeNo(),((EmplEntity)num).getUserId());
        }
        return empMap;
    }

    // 组装单签/或
    public LinkedHashMap<String, String> genEmpl(List<EmplEntity> entities){
        if (CollectionUtils.isEmpty(entities)) return null;
        LinkedHashMap<String, String> empMap = new LinkedHashMap<>();
        for(EmplEntity num :entities){
            empMap.put(num.getEmployeeNo(),num.getUserId());
        }
        return empMap;
    }

    public ApprovalProcessEntity buildApprovalProcess(ApplyApprovalDTO applyApprovalDTO){
        ApprovalProcessEntity entity = new ApprovalProcessEntity();
        // 申请人
        entity.setApplicant(applyApprovalDTO.getEmployeeNo());
        // 申请内容
        //entity0.setApplicationContent("西丽总部-技术部-系统开发-仓库组-Java开发工程师 调动为 西丽总部-技术部-系统开发-仓库组-测试工程师");
        // 申请时间
        entity.setApplicationDate(new Date());
        // 申请关联ID
        //entity.setApplicationId();
        // 申请类型  1-转正申请 2-延期转正申请 3-调职申请 4-离职申请
        entity.setApplicationType(ApplicationTypeEnum.build(applyApprovalDTO.getApplyCode()).getCode());
        // 当前处理人
        //entity0.setApprover();
        // 审批流序号
        entity.setOrderNumber(1);
        entity.setProcessStep(ProcessStepEnum.APPLICATION.getCode());
        // 流程类型 1-申请 2-审批
        entity.setProcessType(ProcessTypeEnum.APPLICATION.getCode());
        // 状态 默认0 1-审批中 2-已通过 3-已驳回 4-已取消
        entity.setStatus(ApproveStatusEnum.WAITTING.getCode());
        return entity;
    }

    public ApplyApprovalDetail buildApprovalProcessDetail(ApplyApprovalDTO applyApprovalDTO,EmplEntity emplEntity ){
        ApplyApprovalDetail applyApprovalDetail = new ApplyApprovalDetail();
        applyApprovalDetail.setName(emplEntity.getName());
        applyApprovalDetail.setApplicationType(ApplicationTypeEnum.build(applyApprovalDTO.getApplyCode()).getCode());
        applyApprovalDetail.setEmployeeNo(emplEntity.getEmployeeNo());
        applyApprovalDetail.setDeptId(emplEntity.getDeptId());
        applyApprovalDetail.setPositionId(emplEntity.getPositionId());
        applyApprovalDetail.setStatus(ApproveStatusEnum.WAITTING.getCode());
        applyApprovalDetail.setHandelStatus(ApproveStatusEnum.WAITTING.getCode());
        List<ApproveFormWorkSchemaInfoDTO> nodes = applyApprovalDTO.getNodes();
        applyApprovalDetail.setDataJson(JSONObject.toJSONString(nodes));
        return applyApprovalDetail;
    }
    // 根据属性名获取值
    public String getNoteVal(List<ApproveFormWorkSchemaInfoDTO> nodes,String name){
        if (StringUtils.isBlank(name) || CollectionUtils.isEmpty(nodes)) return null;
        for(ApproveFormWorkSchemaInfoDTO dto:nodes){
            if (StringUtils.isBlank(dto.getName())) continue;
            if(StringUtils.equalsIgnoreCase(dto.getName().trim(),name)){
                return dto.getValue().toString();
            }
        }
        return null;
    }

    // 上传附件和图片
    @Override
    public String uploadFile(String image, MultipartFile multipartFile) {
        if (null == multipartFile || org.apache.commons.lang.StringUtils.isBlank(multipartFile.getOriginalFilename())) {
            return null;
        }

        // 简历附件大于10mb, 不允许上传
        Asserts.isFalse(multipartFile.getSize() > CommonConstant.MAX_FILE_SIZE, CErrorCode.REQUEST_PARAM_ERROR, "附件大于10mb, 不允许上传");

        // 简历附件文件格式校验, 格式不对, 抛异常
        String originalFilename = multipartFile.getOriginalFilename();
        /*int index = originalFilename.lastIndexOf(".");
        final String extension;
        if ((index > 0) && (index < (originalFilename.length() - 1))) {
            extension = originalFilename.substring(index + 1).toLowerCase();
        } else {
            extension = null;
        }

        Asserts.isFalse(
                StringUtils.isBlank(extension) || !("doc".equalsIgnoreCase(extension)
                        || "docx".equalsIgnoreCase(extension) || "pdf".equalsIgnoreCase(extension)
                        || CommonConstant.IMG_EXTENSIONS.contains(extension)),
                CErrorCode.REQUEST_PARAM_ERROR, "只能上传word,pdf文档和图片");*/
        //图片对接oss
        if (StringUtils.equalsIgnoreCase(image,"image")){
            try {
                return OssFileUtils.uploadOssFile(multipartFile.getBytes(),SeaweedFileConstant.APPROVAL_MODULE+"/"+originalFilename);
            } catch (IOException e) {
                log.error("上传图片失败："+e.getMessage(),e);
                throw new RuntimeException(e);
            }
        }else {
            return FmsFileUtils.uploadFile(multipartFile, SeaweedFileConstant.APPROVAL_MODULE);
        }
    }

    // 取消
    @Override
    @Transactional(value = "erpHrDataTransactionManager", rollbackFor = BusinessException.class)
    public void cancel(CanalApproveDTO canalApproveDTO) {
        log.info("撤销审批，当前撤销参数信息：{}",JSON.toJSONString(canalApproveDTO));
        switch (ApplicationTypeEnum.build(canalApproveDTO.getApplicationType())){
            case EXTENDED_PROBATION:
            case REGULAR:
                regularApproveService.cancel(canalApproveDTO);
                break;
            case TRANSFER:
                transferApproveService.cancel(canalApproveDTO);
                break;
            case RECRUITMENT_APPLICATION:
                recruitmentNeedService.cancel(canalApproveDTO);
                break;
            case LEAVE_APPLY:
                leaveApproveService.cancel(canalApproveDTO);
                break;
            default:
                ApplyApprovalDetail applyApprovalDetail = applyApprovalDetailService.getById(canalApproveDTO.getId());
                Asserts.isFalse(applyApprovalDetail == null, CErrorCode.REQUEST_PARAM_ERROR, "数据不存在");
                // 接口幂等，已撤回的直接返回成功
                if (ApproveStatusEnum.CANCEL.getCode().equals(applyApprovalDetail.getStatus()))  return;

                //钉钉操作不判断
                if (!canalApproveDTO.getSystemFlag()) {
                    Asserts.isTrue(ApproveStatusEnum.WAITTING.getCode().equals(applyApprovalDetail.getStatus()), CErrorCode.REQUEST_PARAM_ERROR, "不是审批中不允许操作");
                    Asserts.isTrue(applyApprovalDetail.getEmployeeNo().equals(DataContextUtils.getEmployeeNo()), CErrorCode.REQUEST_PARAM_ERROR, "不是本人不允许操作");
                }
                ApplyApprovalDetail updateRegular = new ApplyApprovalDetail();
                updateRegular.setId(applyApprovalDetail.getId());
                updateRegular.setStatus(ApproveStatusEnum.CANCEL.getCode());
                boolean bool = applyApprovalDetailService.updateById(updateRegular);
                if (bool) {
                    approvalProcessService.cancel(canalApproveDTO);
                    OperationLogUtils.log(canalApproveDTO.getId().toString(), "取消申请", OperationTypeEnum.UPDATE, LogModuleEnum.APPLY_APPROVE);
                }
        }
    }

    // 添加评论
    @Override
    @Transactional(value = "erpHrDataTransactionManager", rollbackFor = BusinessException.class)
    public void addApproveComment(AddApproveCommentDTO addApproveCommentDTO,EmplEntity emplEntity) throws Exception {

        //组装需要发送消息的userId以及内容
        if (emplEntity == null && StringUtils.isNotBlank(addApproveCommentDTO.getEmpNo()))
            emplEntity = emplService.getByEmployeeNo(addApproveCommentDTO.getEmpNo());
        if (emplEntity == null || StringUtils.isBlank(emplEntity.getUserId()))
            throw new RuntimeException("用户[" + addApproveCommentDTO.getEmpNo() + "]不存在！");

        //添加评论
        ApprovalProcessEntity approvalProcessEntity = null;
        if (addApproveCommentDTO.getIsDing()) {
            // 根据用户匹配
            //获取当前申请的所有审批记录
            Asserts.isFalse(StringUtils.isBlank(addApproveCommentDTO.getProcessInstanceId()), CErrorCode.REQUEST_PARAM_ERROR, "钉钉回调实例为空");
            List<ApprovalProcessEntity> processEntityList = approvalProcessService.list(new QueryWrapper<ApprovalProcessEntity>()
                    .eq("process_instance_id", addApproveCommentDTO.getProcessInstanceId()));
            Asserts.isFalse(CollectionUtils.isEmpty(processEntityList), CErrorCode.REQUEST_PARAM_ERROR, "钉钉回调实例对应审批数据为空");
            processEntityList.sort(new Comparator<ApprovalProcessEntity>() {
                @Override
                public int compare(ApprovalProcessEntity o1, ApprovalProcessEntity o2) {
                    return o1.getOrderNumber().compareTo(o2.getOrderNumber());
                }
            });
            for (ApprovalProcessEntity processEntity : processEntityList) {
                if (processEntity.getOrderNumber() == 1 && StringUtils.equalsIgnoreCase(processEntity.getApplicant(), emplEntity.getEmployeeNo())) {
                    approvalProcessEntity = processEntity;
                    break;
                } else if (processEntity.getOrderNumber() != 1 && StringUtils.equalsIgnoreCase(processEntity.getApprover(), emplEntity.getEmployeeNo())) {
                    approvalProcessEntity = processEntity;
                    break;
                }
            }
        } else {
            approvalProcessEntity = approvalProcessService.getById(addApproveCommentDTO.getApprovalProcessId());
        }
        Asserts.isFalse(approvalProcessEntity == null, CErrorCode.REQUEST_PARAM_ERROR, "数据不存在");
        addApproveCommentDTO.setProcessInstanceId(approvalProcessEntity.getProcessInstanceId());

        addApproveCommentDTO.setCommentUserId(emplEntity.getUserId());
        List<ProcessContentDTO> processContentDTOs = approvalProcessEntity.getProcessContentDTOs();
        if (StringUtils.isNotBlank(addApproveCommentDTO.getText()) || StringUtils.isNotBlank(addApproveCommentDTO.getUrl())){
            // 钉钉回调 需校验评论已存在
            if (addApproveCommentDTO.getIsDing()) {
                final String empName = emplEntity.getName();
                boolean bool = processContentDTOs.stream().anyMatch(p -> StringUtils.isNotBlank(p.getProcessContent())
                        && StringUtils.contains(addApproveCommentDTO.getText(),p.getProcessContent())
                        && StringUtils.contains(addApproveCommentDTO.getText(),p.getUrl())
                        && StringUtils.equalsIgnoreCase(p.getName(), empName));
                if (bool) return;

            }
            processContentDTOs.add(new ProcessContentDTO(addApproveCommentDTO.getText(), addApproveCommentDTO.getUrl(), emplEntity.getName(), new Date()));
        }
        ApprovalProcessEntity updateEntity = new ApprovalProcessEntity();
        updateEntity.setId(approvalProcessEntity.getId());
        updateEntity.setProcessContent(JSONObject.toJSONString(processContentDTOs));
        approvalProcessService.updateById(updateEntity);
        // 钉钉回调，不再继续调钉钉接口
        if (!addApproveCommentDTO.getIsDing())
            DingDingClientUtils.addApproveComment(addApproveCommentDTO);
    }

    // 钉钉回调
    @Override
    @Transactional(value = "erpHrDataTransactionManager", rollbackFor = BusinessException.class)
    public void DingDingApproveCallBackEvent(DingDingResignApproveCallBackEventDTO dto) throws Exception {
        // 审批实例id
        String processInstanceId = dto.getProcessInstanceId();
        try {
            // 审批标题，用于区分审批是哪种类型（这里是审批业务类型，比如转正、离职~等）
            String title = dto.getTitle();
            // 审批类型（这里是对应的审批操作类型）
            String eventType = dto.getEventType();
            /** 如果是审批实例 那么就是说员工userId / 如果是审批任务那么就是审批人的userId */
            String userId = dto.getStaffId();
            String type = dto.getType();

            boolean tryLockResult = redissonTemplate.tryLock("hr_process_instance_code_" + processInstanceId,
                    TimeUnit.SECONDS, 30, 60);
            if (tryLockResult) {
                /**
                 * 获取审批实例详情，里面的字段信息注解可以参考
                 * 
                 * @see com.estone.erp.erp_hr.biz.approve_n.dto.ProcessInstanceDTO
                 */
                OapiProcessinstanceGetResponse processDetails = DingDingClientUtils
                        .getProcessDetails(processInstanceId);
                if (processDetails == null) {
                    return;
                }
                EmplEntity emplEntity = emplService
                        .getOne(Wrappers.<EmplEntity> lambdaQuery().eq(EmplEntity::getUserId, userId));

                if (emplEntity == null) {
                    log.info("当前监听审批事件userId下员工为空：{}", userId);
                    return;
                }

                OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance = processDetails
                        .getProcessInstance();
                // 发起人userId
                String originatorUserid = processInstance.getOriginatorUserid();
                if (DingEventEnum.BPMS_INSTANCE_CHANGE.getCode().equals(eventType)) {
                    // 审批实例
                    if (DingDingEventTypeEnum.START.getName().equals(type)) {

                        log.info("===================钉钉新创建审批同步系统,审批实例id：{}", processInstanceId);
                        boolean createResult = false;
                        if (title.contains("离职")) {
                            createResult = resignApproveService.createByProcessInstance(processInstanceId,
                                    processInstance, emplEntity);
                        }
                        else if (StringUtils.contains(JSON.toJSONString(processInstance), "请假")) {
                            createResult = leaveApproveService.createByProcessInstance(processInstanceId,
                                    processInstance, emplEntity);
                        }
                        log.info("===================钉钉新创建审批同步系统是否成功：{},审批实例id：{}", createResult, processInstanceId);

                    }
                    else if (DingDingEventTypeEnum.FINISH.getName().equals(type)) {
                        log.info("===================审批实例正常结束（同意或拒绝）");
                        boolean flag = false;
                        if (title.contains("离职")) {
                            flag = resignApproveService.dingDingFinish(processInstanceId, dto.getResult());
                        }
                        else if (StringUtils.contains(JSON.toJSONString(processInstance), "请假")) {
                            flag = leaveApproveService.dingDingFinish(processInstanceId, dto.getResult());
                        }
                        log.info("===================审批正常结束（同意或拒绝）：是否通过且修改成功：{}", flag);

                    }
                    else if (DingDingEventTypeEnum.TERMINATE.getName().equals(type)) {

                        // 审批终止（发起人撤销审批单）
                        List<ApprovalProcessEntity> approvalProcessEntityList = approvalProcessService
                                .getListByProcessInstanceId(processInstanceId);
                        if (CollectionUtils.isNotEmpty(approvalProcessEntityList)) {
                            ApprovalProcessEntity approvalProcessEntity = approvalProcessEntityList.get(0);
                            Long applicationId = approvalProcessEntity.getApplicationId();
                            Integer applicationType = approvalProcessEntity.getApplicationType();
                            // 撤销参数对象都一致
                            CanalApproveDTO canalApproveDTO = new CanalApproveDTO();
                            canalApproveDTO.setId(applicationId);
                            canalApproveDTO.setApplicationType(applicationType);
                            canalApproveDTO.setSystemFlag(true);
                            cancel(canalApproveDTO);
                            log.info("===================审批终止（发起人撤销审批单）审批实例id：{},审批实例系统id：{}，审批实例类型：{},第一条审批单信息:{}",
                                    processInstanceId, applicationId, applicationType,
                                    JSON.toJSONString(approvalProcessEntity));
                        }
                        else {
                            log.info("===================审批终止（发起人撤销审批单）当前实例id下审批不存在系统~，ProcessInstanceId：{}",
                                    processInstanceId);
                        }
                    }

                }
                else if (DingEventEnum.BPMS_TASK_CHANGE.getCode().equals(eventType)) {
                    // 审批任务
                    log.info("===================审批任务：实例id{}", processInstanceId);
                    DingDingEventDTO dingDingeventDTO = new DingDingEventDTO();
                    dingDingeventDTO.setProcessInstanceId(processInstanceId);
                    dingDingeventDTO.setProcessInstance(processInstance);
                    dingDingeventDTO.setEmplEntity(emplEntity);
                    dingDingeventDTO.setDingDingResignApproveCallBackEventDTO(dto);
                    if (StringUtils.contains(JSON.toJSONString(processInstance), "请假")) {
                        SysMqSendUtils.sendToDingDingApproveProcessInstanceQueue(dingDingeventDTO);
                        return;
                    }

                    ApplicationTypeEnum applicationTypeEnum = ApplicationTypeEnum.getApplicationTypeEnumByName(title);
                    if (applicationTypeEnum == null) {
                        log.info("===================审批任务：实例id{},标题title{},未对接", processInstanceId, title);
                        return;
                    }
                    dingDingeventDTO.setApplicationType(applicationTypeEnum.getCode());
                    SysMqSendUtils.sendToDingDingApproveProcessInstanceQueue(dingDingeventDTO);
                }
            }
            else {
                throw new BusinessException(CErrorCode.REQUEST_PARAM_ERROR,
                        "DingDingApproveCallBackEvent方法获取redis锁失败~，稍后重试，审批实例id：" + processInstanceId + "");
            }

        }
        catch (Exception e) {
            log.error("DingDingApproveCallBackEvent钉钉回调异常：{}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
        finally {
            redissonTemplate.unlock("hr_process_instance_code_" + processInstanceId);
        }
    }

    /**
     * @Description 根据钉钉同步在系统上的离职审批，修改审批流
     * @Date 2022/3/18 16:08
     * @param: processInstanceId:审批实例id
     * @param: processInstance审批实例
     * @param: emplEntity ：申请人
     * @return boolean
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByProcessInstance(String processInstanceId, OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance, EmplEntity emplEntity, DingDingResignApproveCallBackEventDTO dto) {
        try{
            boolean tryLockResult = redissonTemplate.tryLock("hr_process_instance_code_" + processInstanceId, TimeUnit.SECONDS, 30, 60);
            if (tryLockResult){
                String type = dto.getType();
                String result = dto.getResult();
                String approver = emplEntity.getEmployeeNo();

                if (StringUtils.contains(JSON.toJSONString(processInstance), "请假")) {
                    boolean flag = leaveApproveService.process(processInstanceId, processInstance, emplEntity, dto,
                            true);
                    log.info("===================审批正常结束（同意或拒绝）：是否通过且修改成功：{}", flag);
                    return true;
                }

                //暂时不对接创建审批任务
                if (DingDingEventTypeEnum.START.getName().equals(type)) {
                    return handleStartEvent(processInstanceId, processInstance, approver);
                }
                else if (DingDingEventTypeEnum.FINISH.getName().equals(type)) {
                    //根据实例id 和 审批人工号 确定id
                    ApprovalProcessEntity approvalProcessEntity = approvalProcessService.getOne(Wrappers.<ApprovalProcessEntity>lambdaQuery()
                            .eq(ApprovalProcessEntity::getProcessInstanceId, processInstanceId).eq(ApprovalProcessEntity::getApprover, approver)
                            .isNotNull(ApprovalProcessEntity::getTaskId));
                    if (approvalProcessEntity == null){
                        log.info("当前已完结审批实例不存在系统，审批实例详情：",JSON.toJSONString(processInstance));
                        return true;
                    }
                    String remark = dto.getRemark();
                    ApprovalProcessDTO approvalProcessDTO = new ApprovalProcessDTO();
                    approvalProcessDTO.setId(approvalProcessEntity.getId());
                    approvalProcessDTO.setProcessContent(remark);
                    approvalProcessDTO.setIsDing(true);
                    //对于已经存在的审批流进行审批
                    if (DingDingEventTypeEnum.AGREE.getName().equals(result)){
                        //同意
                        approvalProcessDTO.setStatus(ApproveStatusEnum.PASS.getCode());
                    }else if (DingDingEventTypeEnum.REFUSE.getName().equals(result)){
                        //拒绝
                        approvalProcessDTO.setStatus(ApproveStatusEnum.REJECT.getCode());
                        //删除考勤审批
                        /*AttendanceProcinst attendanceProcinst = new AttendanceProcinst();
                        attendanceProcinst.setProcinstId(processInstanceId);
                        attendanceProcinst.setType(false);
                        dingMqSendUtils.sendHrAttendanceProcinstQueue(attendanceProcinst);*/
                    }
                    boolean processResult = approvalProcessService.process(approvalProcessDTO);
                    log.info("{}：{}了审批(实例id)：{},审批是否通过：{}，备注：{}",approver,ApproveStatusEnum.getNameByCode(approvalProcessDTO.getStatus()),
                            approvalProcessEntity.getProcessInstanceId(),processResult,remark);
                }else if (DingDingEventTypeEnum.CANCEL.getName().equals(type)){
                    //todo 审批并签、或签 暂时不对接，上流程已处理
                    return true;
                }else if (DingDingEventTypeEnum.COMMENT.getName().equalsIgnoreCase(type)){
                    // 添加评论
                    AddApproveCommentDTO addApproveCommentDTO = new AddApproveCommentDTO();
                    addApproveCommentDTO.setIsDing(true);
                    addApproveCommentDTO.setProcessInstanceId(processInstanceId);
                    addApproveCommentDTO.setEmpNo(emplEntity.getEmployeeNo());
                    addApproveCommentDTO.setText(dto.getContent());
                    addApproveComment(addApproveCommentDTO,emplEntity);
                }
                return true;
            }else {
                throw new BusinessException(CErrorCode.REQUEST_PARAM_ERROR, "updateByProcessInstance方法获取redis锁失败~，稍后重试，审批实例id："+processInstanceId+"");
            }
        }catch (Exception e){
            e.printStackTrace();
        }finally{
            redissonTemplate.unlock("hr_process_instance_code_" + processInstanceId);
        }
        return false;
    }

    private boolean handleStartEvent(String processInstanceId,
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance, String approver) {
        if (StringUtils.isBlank(processInstanceId) || processInstance == null || StringUtils.isBlank(approver)) {
            return false;
        }
        // 暂时只对接离职、请假
        if (!StringUtils.contains(processInstance.getTitle(), "离职")
                && !StringUtils.contains(JSON.toJSONString(processInstance), "请假")) {
            return true;
        }
        // 审批任务开始，需要新建审批流,根据实例id，从orderNumber最大的那条新增
        List<ApprovalProcessEntity> approvalProcessEntitys = approvalProcessService
                .list(Wrappers.<ApprovalProcessEntity> lambdaQuery().eq(ApprovalProcessEntity::getProcessInstanceId,
                        processInstanceId));

        if (CollectionUtils.isEmpty(approvalProcessEntitys)) {
            return true;
        }

        // tasks 审批节点会有重复的情况，我们只需要判断审批流里同一审批实例id并且有当前审批人，有则返回不做记录
        Optional<ApprovalProcessEntity> firstOptional = approvalProcessEntitys.stream().filter(
                item -> item.getApprover().equals(approver) && item.getProcessInstanceId().equals(processInstanceId))
                .findFirst();
        if (firstOptional.isPresent()) {
            log.info("当前task审批任务审批人在系统审批流已存在，审批实例id：{}，审批人工号：{}", processInstanceId, approver);
            return true;
        }

        approvalProcessEntitys.sort(Comparator.comparing(ApprovalProcessEntity::getOrderNumber).reversed());
        ApprovalProcessEntity approvalProcessEntity = approvalProcessEntitys.get(0);

        List<OapiProcessinstanceGetResponse.TaskTopVo> tasks = processInstance.getTasks();
        // 倒序获取最大的
        tasks.sort((a, b) -> Long.valueOf(b.getTaskid()).compareTo(Long.valueOf(a.getTaskid())));
        OapiProcessinstanceGetResponse.TaskTopVo taskTopVo = tasks.get(0);

        ApprovalProcessEntity entityNext = BuildProcessUtil.buildApproveEntity(approvalProcessEntity.getApplicant(),
                approvalProcessEntity.getApplicationContent(), approvalProcessEntity.getApplicationType(),
                approvalProcessEntity.getApplicationDate(), approver, processInstanceId, taskTopVo.getCreateTime());

        entityNext.setApplicationId(approvalProcessEntity.getApplicationId());
        // taskId任务节点id,第一条没有
        entityNext.setTaskId(taskTopVo.getTaskid());
        // 审批流序号
        entityNext.setOrderNumber(approvalProcessEntity.getOrderNumber() + 1);
        entityNext.setProcessStep(ProcessStepEnum.DING_APPROVE.getCode());
        // 流程类型 1-申请 2-审批
        entityNext.setProcessType(ProcessTypeEnum.APPROVE.getCode());
        // 状态 默认0 1-审批中 2-已通过 3-已驳回 4-已取消
        entityNext.setStatus(ApproveStatusEnum.WAITTING.getCode());
        approvalProcessService.save(entityNext);
        log.info("{}：的审批任务(实例id)：{},备注：{}", approver, approvalProcessEntity.getProcessInstanceId());
        return true;
    }

    // 审批
    @Override
    public boolean process(ApprovalProcessEntity process ,ApprovalProcessEntity nextProcess, ApprovalProcessDTO approvalProcessDTO) {
        ApplyApprovalDetail applyApprovalDetail = applyApprovalDetailService.getById(process.getApplicationId());
        Asserts.isFalse(applyApprovalDetail == null, CErrorCode.REQUEST_PARAM_ERROR, "数据不存在");
        //审批状态
        Integer status = process.getStatus();
        ApplyApprovalDetail updateDetail = new ApplyApprovalDetail();
        updateDetail.setId(applyApprovalDetail.getId());

        //最后一步为空（审批完成）或者拒绝才改变状态
        if (nextProcess == null || ApproveStatusEnum.REJECT.getCode().equals(status)) {
            updateDetail.setStatus(status);
        }

        boolean result = applyApprovalDetailService.updateById(updateDetail);
        if (result) {
            //调用钉钉审批接口
            String taskId = process.getTaskId();
            String approverUserId = process.getApproverUserId();
            String processInstanceId = process.getProcessInstanceId();
            //调用钉钉审批接口,非钉钉回调、当前审批节点taskId不为空、当前审批节点人userId、钉钉审批实例id不为空
            if (!approvalProcessDTO.getIsDing() && StrUtil.isNotBlank(taskId)
                    && StrUtil.isNotBlank(approverUserId) && StrUtil.isNotBlank(processInstanceId)){

                ExecuteApproveDTO executeApproveDTO = new ExecuteApproveDTO();
                executeApproveDTO.setTaskId(Long.parseLong(taskId));
                executeApproveDTO.setUserId(approverUserId);
                executeApproveDTO.setRemark(approvalProcessDTO.getProcessContent());
                executeApproveDTO.setProcessInstanceId(processInstanceId);
                //审批同意或拒绝
                if (ApproveStatusEnum.PASS.getCode().equals(status)){
                    executeApproveDTO.setResult(DingDingEventTypeEnum.AGREE.getName());
                }else if (ApproveStatusEnum.REJECT.getCode().equals(status)){
                    executeApproveDTO.setResult(DingDingEventTypeEnum.REFUSE.getName());
                }
                try {
                    OapiProcessinstanceExecuteV2Response oapiProcessinstanceExecuteV2Response = DingDingClientUtils.executeApproveDTO(executeApproveDTO);
                    if (oapiProcessinstanceExecuteV2Response.isSuccess()) {
                        log.info("调用钉钉接口审批审批实例成功，参数：{}" , executeApproveDTO.toString());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("调用钉钉接口审批审批实例失败,参数：{}，失败原因：{}",executeApproveDTO.toString(),e.getMessage());
                    throw new RuntimeException("调用钉钉接口审批审批实例失败" + executeApproveDTO.toString() + e.getMessage());
                }
            } /*else {
                throw new BusinessException(CErrorCode.REQUEST_PARAM_ERROR, "操作失败,发起钉钉审批参数错误！");
            }*/
        } else {
            throw new BusinessException(CErrorCode.REQUEST_PARAM_ERROR, "操作失败");
        }
        return result;
    }

}
