package com.estone.erp.erp_hr.biz.attendance.service.impl;

import com.aliyun.dingtalkattendance_1_0.models.GetLeaveRecordsResponse;
import com.aliyun.dingtalkattendance_1_0.models.GetLeaveRecordsResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dingtalk.api.response.OapiAttendanceVacationQuotaUpdateResponse;
import com.estone.erp.erp_hr.biz.attendance.constant.LeavePlatformStatusEnum;
import com.estone.erp.erp_hr.biz.attendance.constant.LeaveRecordTypeEnum;
import com.estone.erp.erp_hr.biz.attendance.constant.LeaveTypeEnum;
import com.estone.erp.erp_hr.biz.attendance.entity.EmpLeaveAdjustmentLog;
import com.estone.erp.erp_hr.biz.attendance.entity.EmpLeaveTransactionRecords;
import com.estone.erp.erp_hr.biz.attendance.mapper.EmpLeaveTransactionRecordsMapper;
import com.estone.erp.erp_hr.biz.attendance.service.EmpLeaveAdjustmentLogService;
import com.estone.erp.erp_hr.biz.attendance.service.EmpLeaveBalanceService;
import com.estone.erp.erp_hr.biz.attendance.service.EmpLeaveTransactionRecordsService;
import com.estone.erp.erp_hr.biz.attendance.util.DateUtil;
import com.estone.erp.erp_hr.biz.employee.entity.EmplEntity;
import com.estone.erp.erp_hr.util.DingDingClientUtils;
import com.estone.erp.erp_hr.util.TokenDataUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 假期消费记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Service
@Slf4j
public class EmpLeaveTransactionRecordsServiceImpl extends ServiceImpl<EmpLeaveTransactionRecordsMapper, EmpLeaveTransactionRecords> implements EmpLeaveTransactionRecordsService {

    @Resource
    private EmpLeaveAdjustmentLogService empLeaveAdjustmentLogService;

    public final Long PAGE_NUMBER =  200L;


    /**
     * 同步假期变动记录
     * @param userids 用户 ID 列表，多个用逗号分隔
     * @param leaveCode 假期类型代码
     * @return 同步成功的假期变动记录列表
     */
    /**
     * 同步假期变动记录
     * @param userids 用户 ID 列表，多个用逗号分隔
     * @param leaveCode 假期类型代码
     * @return 同步成功的假期变动记录列表
     */
    public void syncLeaveTransactionRecords(String userids) {
        long pageNumber = 0;
        boolean hasMore = true;

        while (hasMore) {
            try {
                GetLeaveRecordsResponse response = DingDingClientUtils.getLeaveRecordsWithOptions(userids, LeaveTypeEnum.COMPENSATORY_LEAVE.getLeaveCode(), pageNumber);
                if (response == null) {
                    hasMore = false;
                    log.warn("DingDingClientUtils.getLeaveRecordsWithOptions 返回 null");
                    continue;
                }

                GetLeaveRecordsResponseBody body = response.getBody();
                if (body == null || !body.success) {
                    hasMore = false;
                    log.warn("DingDingClientUtils.getLeaveRecordsWithOptions 返回的响应体为空");
                    continue;
                }
                GetLeaveRecordsResponseBody.GetLeaveRecordsResponseBodyResult result = body.getResult();
                List<GetLeaveRecordsResponseBody.GetLeaveRecordsResponseBodyResultLeaveRecords> items = result.getLeaveRecords();
                if (items != null && !items.isEmpty()) {
                    List<EmpLeaveTransactionRecords> batchRecords = new ArrayList<>();
                    for (GetLeaveRecordsResponseBody.GetLeaveRecordsResponseBodyResultLeaveRecords item : items) {
                        if (item == null) {
                            log.warn("响应体中的列表项为 null，跳过该记录");
                            continue;
                        }
                        if ((LeaveRecordTypeEnum.MODIFY_QUOTA.getCode().equals(item.getLeaveRecordType()) ||
                            (LeaveRecordTypeEnum.LEAVE.getCode().equals(item.getLeaveRecordType()) && (LeavePlatformStatusEnum.SUCCESS.getCode().equals(item.getLeaveStatus())
                                    || LeavePlatformStatusEnum.REVOKE.getCode().equals(item.getLeaveStatus()) ))) && item.getRecordNumPerHour() > 0)  {
                            EmpLeaveTransactionRecords record = convertToEmpLeaveTransactionRecords(item);
                            batchRecords.add(record);
                        }
                    }
                    // 批量保存记录
                    if (!batchRecords.isEmpty()) {
                        // 批量查询所有已存在的记录
                        List<String> recordIds = batchRecords.stream().map(e -> e.getRecordId()).collect(Collectors.toList());
                        Map<String, EmpLeaveTransactionRecords> existRecordsMap = this.list(
                                new LambdaQueryWrapper<EmpLeaveTransactionRecords>().in(EmpLeaveTransactionRecords::getRecordId, recordIds)
                        ).stream().collect(Collectors.toMap(EmpLeaveTransactionRecords::getRecordId, record -> record));
                        
                        List<EmpLeaveTransactionRecords> toUpdate = new ArrayList<>();
                        List<EmpLeaveTransactionRecords> toInsert = new ArrayList<>();
                        for (EmpLeaveTransactionRecords record : batchRecords) {
                            EmpLeaveTransactionRecords existRecord = existRecordsMap.get(record.getRecordId());
                            if (existRecord != null) {
                                if (!StringUtils.equalsIgnoreCase(record.getLeaveStatus(), existRecord.getLeaveStatus())) {
                                    toUpdate.add(record);
                                    record.setId(existRecord.getId());
                                }
                            } else {
                                toInsert.add(record);
                            }
                        }
                        if (!toInsert.isEmpty()) {
                            this.saveBatch(toInsert, 100);
                        }
                        if (!toUpdate.isEmpty()) {
                            this.updateBatchById(toUpdate, 100);
                        }
                    }
                }
                // 检查 hasMore 字段是否为空
                Boolean hasMoreValue = result.getHasMore();
                hasMore = hasMoreValue != null && hasMoreValue;
                pageNumber+=PAGE_NUMBER;
            } catch (Exception e) {
                hasMore = false;
                log.error("同步假期变动记录时发生异常，当前页码: {}, 用户 ID: {}", pageNumber, userids, e);
            }
        }
    }



    private EmpLeaveTransactionRecords convertToEmpLeaveTransactionRecords(GetLeaveRecordsResponseBody.GetLeaveRecordsResponseBodyResultLeaveRecords item) {
        EmpLeaveTransactionRecords record = new EmpLeaveTransactionRecords();
        record.setUserId(item.getUserId());
        record.setLeaveCode(item.getLeaveCode());
        record.setStartTime(item.getStartTime());
        record.setEndTime(item.getEndTime());
        record.setCalType(item.getCalType());
        record.setLeaveReason(item.getLeaveReason());
        record.setLeaveRecordType(item.getLeaveRecordType());
        record.setLeaveStatus(item.getLeaveStatus());
        record.setLeaveViewUnit(item.getLeaveViewUnit());
        record.setOpUserId(item.getOpUserId());
        record.setRecordId(item.getRecordId());
        record.setRecordNumPerDay(item.getRecordNumPerDay());
        record.setRecordNumPerHour(item.getRecordNumPerHour());
        record.setGmtCreate(item.getGmtCreate());
        record.setGmtModified(item.getGmtModified());
        record.setQuotaId(item.getQuotaId());
        // 设置过期时间为创建时间后一年
        if (item.getGmtCreate() != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(item.getGmtCreate());
            calendar.add(Calendar.YEAR, 1);
            record.setExpireDate(calendar.getTime());
        }
        return record;
    }

    @Override
    @Transactional
    public void clearOperation(EmpLeaveTransactionRecords record) {
        try {
            // 批量更新剩余小时数为0
            Long surplusNumPerHour = record.getSurplusNumPerHour();
            update(new LambdaUpdateWrapper<EmpLeaveTransactionRecords>()
                .eq(EmpLeaveTransactionRecords::getId, record.getId())
                .set(EmpLeaveTransactionRecords::getSurplusNumPerHour, 0L));
            // 调用平台接口
            OapiAttendanceVacationQuotaUpdateResponse response = DingDingClientUtils.updateLeaveBalanceFromAPI(record.getUserId(),
                    LeaveTypeEnum.COMPENSATORY_LEAVE.getLeaveCode(), -surplusNumPerHour,record);
            if (response == null || !response.isSuccess()) {
              throw new RuntimeException("调用钉钉接口失败");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
