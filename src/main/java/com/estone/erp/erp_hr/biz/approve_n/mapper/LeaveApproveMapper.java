package com.estone.erp.erp_hr.biz.approve_n.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.estone.erp.erp_hr.biz.approve_n.entity.LeaveApprove;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 请假申请表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Mapper
public interface LeaveApproveMapper extends BaseMapper<LeaveApprove> {

    List<Long> listApplicationId(@Param("permissionEmployeeNos") Set<String> permissionEmployeeNos);

}
