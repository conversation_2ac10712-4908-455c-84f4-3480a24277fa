package com.estone.erp.erp_hr.biz.attendance.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.erp_hr.biz.attendance.dto.LeaveBalanceQueryDto;
import com.estone.erp.erp_hr.biz.attendance.entity.EmpLeaveAdjustmentLog;
import com.estone.erp.erp_hr.biz.attendance.service.EmpLeaveAdjustmentLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 假期额度调剂日志表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
@RestController
@RequestMapping("/empLeaveAdjustmentLog")
@Slf4j
public class EmpLeaveAdjustmentLogController {

    @Resource
    private EmpLeaveAdjustmentLogService empLeaveAdjustmentLogService;;

    /**
     * 查询员工假期清零处理
     * @param queryParam 查询参数
     * @return 员工假期余额列表
     */
    @PostMapping("/list")
    public ApiResult<?> queryEmpLeaveAdjustmentLog(@RequestBody LeaveBalanceQueryDto queryParam) {
        try {
            Page<EmpLeaveAdjustmentLog> empLeaveAdjustmentLogPage = empLeaveAdjustmentLogService.queryLeaveAdjustmentLog(queryParam);
            return ApiResult.newSuccess(empLeaveAdjustmentLogPage);
        } catch (Exception e) {
            log.error("查询假期清零异常: {}", e.getMessage());
            return ApiResult.newError("查询假期清零异常: " + e.getMessage());
        }
    }

}

