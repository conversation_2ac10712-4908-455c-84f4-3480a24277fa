package com.estone.erp.erp_hr.biz.attendance.constant;

/**
 * 请假平台状态
 * <AUTHOR>
 * @date 2021/9/28/18:05
 */
public enum LeavePlatformStatusEnum {

    INIT("init", "请假申请中"),
    SUCCESS("success", "请假并已通过"),
    REFUSE("refuse", "请假但被被拒"),
    ABORT("abort", "请假撤销"),
    REVOKE("revoke", "请假已通过但是撤销了请假并已同意");

    private final String code;
    private final String type;

    LeavePlatformStatusEnum(String code, String type){
        this.code = code;
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public String getType() {
        return type;
    }

    public static String getTypeName(String code){
        if (code == null){
            return "";
        }
        switch (code){
            case "init" :
                return INIT.getType();
            case "success" :
                return SUCCESS.getType();
            case "refuse" :
                return REFUSE.getType();
            case "abort" :
                return ABORT.getType();
            case "revoke" :
                return REVOKE.getType();
        }
        return "";
    }
}