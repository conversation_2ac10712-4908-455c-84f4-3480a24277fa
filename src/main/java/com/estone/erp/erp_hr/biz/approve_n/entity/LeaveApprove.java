package com.estone.erp.erp_hr.biz.approve_n.entity;

import com.baomidou.mybatisplus.annotation.IdType;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.estone.erp.erp_hr.biz.approve_n.enums.ApproveStatusEnum;
import com.estone.erp.erp_hr.biz.attendance.constant.LeaveTypeEnum;
import com.estone.erp.erp_hr.common.util.CacheUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang.StringUtils;

/**
 * <p>
 * 请假申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="LeaveApprove对象", description="请假申请表")
public class LeaveApprove implements Serializable {


    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    @ApiModelProperty(value = "部门")
    private Long deptId;

    @ApiModelProperty(value = "职位/岗位ID")
    private Long positionId;

    @ApiModelProperty(value = "直系上级")
    private String superior;

    @ApiModelProperty(value = "开始时间")
    private Date startDate;

    @ApiModelProperty(value = "结束时间")
    private Date endDate;

    @ApiModelProperty(value = "时长")
    private Double duration;

    @ApiModelProperty(value = "请假类型")
    private Integer leaveType;

    @ApiModelProperty(value = "请假原因")
    private String leaveReason;

    @ApiModelProperty(value = "钉钉创建的审批实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "附件")
    private String attachment;

    @ApiModelProperty(value = "状态 0-待审批 1-审批中 2-已通过 3-已驳回 4-已取消")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "完成时间")
    private Date completeDate;

    @ApiModelProperty(value = "当前处理人")
    private String approver;

    @ApiModelProperty(value = "修改人")
    private String updator;

    @ApiModelProperty(value = "修改时间")
    private Date updatedDate;

    @ApiModelProperty(value = "年假余额")
    private Double leaveBalance;

    @ApiModelProperty(value = "假期单位（小时/天）")
    private String leaveUnit;

    public String getLeaveTypeName(){
        return LeaveTypeEnum.getNameByCode(leaveType);
    }

    public String getStatusName() {
        return ApproveStatusEnum.getNameByCode(status);
    }

    /**
     * 当前处理人
     */
    public String getApproverName() {
        if (StringUtils.isNotBlank(approver)) {
            return CacheUtils.getEmplName(approver);
        }
        return null;
    }

    /**
     * 部门名称
     */
    public String getDeptName() {
        if (deptId != null) {
            return CacheUtils.getDeptName(deptId);
        }
        return null;
    }

    /**
     * 目前只用管年假余额，直接返回余额
     * @return
     */
    public Double getBeforeBalance() {
        return leaveBalance;
    }
}
