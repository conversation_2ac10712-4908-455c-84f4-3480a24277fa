package com.estone.erp.erp_hr.biz.approve_n.controller;

import java.util.*;

import javax.annotation.Resource;

import com.estone.erp.erp_hr.biz.attendance.constant.LeaveTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CErrorCode;
import com.estone.erp.common.mongo.model.CQuery;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.erp_hr.biz.approve_n.dto.ApprovalProcessDTO;
import com.estone.erp.erp_hr.biz.approve_n.dto.DingDingResignApproveCallBackEventDTO;
import com.estone.erp.erp_hr.biz.approve_n.dto.LeaveApproveDTO;
import com.estone.erp.erp_hr.biz.approve_n.entity.LeaveApprove;
import com.estone.erp.erp_hr.biz.approve_n.enums.ApproveStatusEnum;
import com.estone.erp.erp_hr.biz.approve_n.enums.DingDingEventTypeEnum;
import com.estone.erp.erp_hr.biz.approve_n.service.LeaveApproveService;
import com.estone.erp.erp_hr.biz.approve_n.vo.LeaveApproveVO;
import com.estone.erp.erp_hr.biz.employee.entity.EmplEntity;
import com.estone.erp.erp_hr.biz.employee.service.EmplService;
import com.estone.erp.erp_hr.common.constant.CommonConstant;
import com.estone.erp.erp_hr.util.DataContextUtils;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 请假申请表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Slf4j
@RestController
@RequestMapping("/leaveApprove")
public class LeaveApproveController {

    @Resource
    private LeaveApproveService leaveApproveService;
    @Resource
    private EmplService emplService;

    /**
     * 请假审批-列表查询
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/list")
    @ApiOperation("请假审批-列表查询")
    public ApiResult<?> listApprovee(
            @RequestBody() @ApiParam(name = "param", value = "页面输入参数模型", required = true) ApiRequestParam<CQuery<LeaveApproveDTO>> param) {
        CQuery<LeaveApproveDTO> query = param.getArgs();
        if (query.getSearch() == null) {
            query.setSearch(new LeaveApproveDTO());
        }
        String employeeNo = DataContextUtils.getEmployeeNo();
        if (StringUtils.isBlank(employeeNo) || CommonConstant.SYSTEM_CREATOR.equals(employeeNo)) {
            return ApiResult.newError("申请人或者审批人不能为空");
        }
        query.getSearch().setPermissionEmployeeNos(Collections.singleton(employeeNo));
        IPage<LeaveApprove> page = leaveApproveService.listLeaveApprove(query);
        return ApiResult.newSuccess(page);
    }

    @PostMapping(value = "/leaveTypeAll")
    @ApiOperation("假期类型")
    public ApiResult<?> getLeaveTypeAll() {
        // 创建TreeMap，通过自然排序
        Map<String, String> sortedLeaveTypeMap = new TreeMap<>(Comparator.comparingInt(Integer::parseInt));
        // 将原始Map的内容加入TreeMap以完成排序
        sortedLeaveTypeMap.putAll(LeaveTypeEnum.LEAVE_CODE_NAMA_MAP);
        return ApiResult.newSuccess(sortedLeaveTypeMap);
    }

    /**
     * 请假审批详情
     *
     * @param id
     * @return
     */
    @PostMapping(value = "/detail/{id}")
    @ApiOperation("请假审批详情")
    public ApiResult<?> detail(@PathVariable(value = "id") Long id) {
        LeaveApproveVO vo = leaveApproveService.detail(id);
        return vo != null ? ApiResult.newSuccess(vo) : ApiResult.newError("数据不存在");
    }

    @PostMapping(value = "/process")
    @ApiOperation("审批")
    public ApiResult<?> process(@RequestBody() ApprovalProcessDTO approvalProcessDTO) {
        log.info("RequestBody : {}", JSON.toJSONString(approvalProcessDTO));
        Asserts.isFalse(approvalProcessDTO == null || approvalProcessDTO.getId() == null,
                CErrorCode.REQUEST_PARAM_ERROR, "参数ID不能为空");
        Asserts.isFalse(approvalProcessDTO.getApprover() == null, CErrorCode.REQUEST_PARAM_ERROR, "审批人不能为空");
        Asserts.isFalse(approvalProcessDTO.getStatus() == null, CErrorCode.REQUEST_PARAM_ERROR, "审批结果不能为空");
        if (!Arrays.asList(ApproveStatusEnum.PASS.getCode(), ApproveStatusEnum.REJECT.getCode())
                .contains(approvalProcessDTO.getStatus())) {
            return ApiResult.newError("审批结果参数错误");
        }
        try {
            LeaveApprove leaveApprove = leaveApproveService.getById(approvalProcessDTO.getId());
            Asserts.isFalse(leaveApprove == null, CErrorCode.REQUEST_PARAM_ERROR, "数据不存在");
            if (!ApproveStatusEnum.WAITTING.getCode().equals(leaveApprove.getStatus())) {
                return ApiResult.newError("非待审批状态");
            }
            if (!StringUtils.equalsIgnoreCase(leaveApprove.getApprover(), approvalProcessDTO.getApprover())) {
                return ApiResult.newError("传入审批人参数" + approvalProcessDTO.getApprover() + "非当前审批人");
            }

            String processInstanceId = leaveApprove.getProcessInstanceId();
            EmplEntity emplEntity = emplService.getOne(Wrappers.<EmplEntity> lambdaQuery().eq(EmplEntity::getEmployeeNo,
                    approvalProcessDTO.getApprover()));
            if (emplEntity == null) {
                return ApiResult.newError("当前审批事件下员工为空：" + approvalProcessDTO.getApprover());
            }
            DingDingResignApproveCallBackEventDTO dto = new DingDingResignApproveCallBackEventDTO();
            dto.setProcessInstanceId(processInstanceId);
            dto.setRemark(approvalProcessDTO.getProcessContent());
            String resulName = DingDingEventTypeEnum.AGREE.getName();
            if (ApproveStatusEnum.REJECT.getCode().equals(approvalProcessDTO.getStatus())) {
                resulName = DingDingEventTypeEnum.REFUSE.getName();
            }
            dto.setResult(resulName);
            dto.setType(DingDingEventTypeEnum.FINISH.getName());

            boolean result = leaveApproveService.process(processInstanceId, null, emplEntity, dto, false);

            return result ? ApiResult.newSuccess() : ApiResult.newError("操作失败");
        }
        catch (Exception e) {
            log.error("审批异常{}", e.getMessage(), e);
            return ApiResult.newError("审批异常：" + e.getMessage());
        }
    }
}
