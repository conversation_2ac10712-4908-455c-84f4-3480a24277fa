package com.estone.erp.erp_hr.biz.attendance.constant;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 假期类型枚举类，包含假期代码和假期名称
 */
public enum LeaveTypeEnum {
    ANNUAL_LEAVE("8e402203-de5b-42c6-b7e4-4d388f33f737", "1","年假"),
    COMPENSATORY_LEAVE("f4a0b490-dc84-4a66-a253-758370d79db3", "2","调休"),
    PERSONAL_LEAVE("19bc5013-d211-4253-94fa-68da42225876", "3","事假"),
    SICK_LEAVE("5feb6cd6-9431-4cab-969e-71a136a30291", "4","病假"),
    WOMEN_LEAVE("d0737d05-e7dc-4e21-bb29-9028253461bf", "5","女生假"),
    MARRIAGE_LEAVE("4b304413-a09b-48b2-acab-22d9ccda1723", "6","婚假"),
    MATERNITY_LEAVE("9d8168d6-88a8-4072-83f1-bfe233d65745", "7","产假"),
    PATERNITY_LEAVE("5bfa2671-b441-4177-be7a-d9f451ef1c4a", "8","陪产假"),
    FUNERAL_LEAVE("c01e49bb-14bc-47a5-a19a-e33d2d52db56", "9","丧假"),
    PRENATAL_CHECK_LEAVE("d9bb971a-e3c1-4142-a2a9-d1ac6466c2a7", "10","产检假"),
    BUSINESS_TRIP("b0cf4fa3-1680-4cab-9400-adc6bb0d1fbc", "11","出差"),
    WORK_INJURY_LEAVE("ece76716-55a6-4332-93a4-500562de1888", "12","工伤假"),
    TRAVEL_LEAVE("7d53b4c1-f5dc-46ba-a360-5b870180832b", "13","路途假"),
    GENERAL_LEAVE_ADJUSTMENT("932c05ed-7500-44e9-bd04-5797b6625fcd", "14","普通假期调休（接口新增测试）"),
    OFFICIAL_OUTING("d7bcc72e-2c39-4b6f-a032-48ded8848b2a", "15","因公外出"),
    ANNUAL_LEAVE_TEST("021796dc-2a9f-4d74-8e1d-003c2b78bfd3", "16","年假测试-测试"),
    COMPENSATORY_LEAVE_TEST("514ab9c6-7574-4fcf-a397-4f876e3ac8e4", "17","调休测试-测试");

    private final String leaveCode;
    private final String code;
    private final String leaveName;

    LeaveTypeEnum(String leaveCode, String code, String leaveName) {
        this.leaveCode = leaveCode;
        this.code = code;
        this.leaveName = leaveName;
    }

    public String getLeaveCode() {
        return leaveCode;
    }

    public String getCode() {
        return code;
    }

    public String getLeaveName() {
        return leaveName;
    }

    private static final Map<String, LeaveTypeEnum> LEAVE_CODE_MAP = new HashMap<>();
    private static final Map<String, LeaveTypeEnum> LEAVE_NAME_MAP = new HashMap<>();
    public static final Map<String, String> LEAVE_CODE_NAMA_MAP = new HashMap<>();

    static {
        for (LeaveTypeEnum leaveType : values()) {
            LEAVE_CODE_MAP.put(leaveType.getLeaveCode(), leaveType);
            LEAVE_NAME_MAP.put(leaveType.getLeaveName(), leaveType);
            LEAVE_CODE_NAMA_MAP.put(leaveType.getCode(),leaveType.getLeaveName());
        }
    }

    /**
     * 根据假期代码获取对应的假期类型枚举
     * @param leaveCode 假期代码
     * @return 对应的假期类型枚举，若未找到则返回 null
     */
    public static LeaveTypeEnum getByLeaveCode(String leaveCode) {
        return LEAVE_CODE_MAP.get(leaveCode);
    }

    /**
     * 根据假期名称获取对应的假期代码
     *
     * @param leaveName 假期名称
     * @return 对应的假期代码，若未找到则返回 null
     */
    public static String getLeaveCodeByName(String leaveName) {
        if (StringUtils.isBlank(leaveName)) {
            return null;
        }
        LeaveTypeEnum leaveType = LEAVE_NAME_MAP.get(leaveName);
        return leaveType != null ? leaveType.getLeaveCode() : null;
    }

    public static Integer getCodeByName(String leaveName) {
        if (StringUtils.isBlank(leaveName)) {
            return null;
        }
        LeaveTypeEnum leaveType = LEAVE_NAME_MAP.get(leaveName);
        return leaveType != null ? Integer.valueOf(leaveType.getCode()) : null;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        return LEAVE_CODE_NAMA_MAP.get(String.valueOf(code));
    }
}
