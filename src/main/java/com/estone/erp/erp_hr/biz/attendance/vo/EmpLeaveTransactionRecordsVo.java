package com.estone.erp.erp_hr.biz.attendance.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class EmpLeaveTransactionRecordsVo  {

    private Long id;

    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    @ApiModelProperty(value = "工号")
    private String employeeNo;

    @ApiModelProperty(value = "部门")
    private String department;

    private String surplusNumPerHourStr;

    private String recordNumPerHourStr;

    @ApiModelProperty(value = "过期时间")
    private Date expireDate;

    private String expireStatusWithDayStr;

    @ApiModelProperty(value = "标准化创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "状态")
    private Integer status;

    public EmpLeaveTransactionRecordsVo() {}
    public EmpLeaveTransactionRecordsVo(long id,String employeeName, String employeeNo, String department,
        String surplusNumPerHourStr, String recordNumPerHourStr, Date expireDate, String expireStatusWithDayStr, Date createdAt, Integer status) {
        this.id = id;
        this.employeeName = employeeName;
        this.employeeNo = employeeNo;
        this.department = department;
        this.expireDate = expireDate;
        this.createdAt = createdAt;
        this.status = status;
        this.surplusNumPerHourStr = surplusNumPerHourStr;
        this.recordNumPerHourStr = recordNumPerHourStr;
        this.expireStatusWithDayStr = expireStatusWithDayStr;
    }




}
