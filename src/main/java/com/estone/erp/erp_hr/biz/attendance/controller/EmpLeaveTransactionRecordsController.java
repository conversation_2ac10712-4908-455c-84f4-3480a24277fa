package com.estone.erp.erp_hr.biz.attendance.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.erp_hr.biz.attendance.constant.CompensatoryLeaveStatusEnum;
import com.estone.erp.erp_hr.biz.attendance.constant.LeaveRecordTypeEnum;
import com.estone.erp.erp_hr.biz.attendance.dto.LeaveBalanceQueryDto;
import com.estone.erp.erp_hr.biz.attendance.entity.EmpLeaveAdjustmentLog;
import com.estone.erp.erp_hr.biz.attendance.entity.EmpLeaveTransactionRecords;
import com.estone.erp.erp_hr.biz.attendance.service.EmpLeaveAdjustmentLogService;
import com.estone.erp.erp_hr.biz.attendance.service.EmpLeaveTransactionRecordsService;
import com.estone.erp.erp_hr.biz.attendance.util.DateUtil;
import com.estone.erp.erp_hr.biz.attendance.vo.EmpLeaveTransactionRecordsVo;
import com.estone.erp.erp_hr.biz.employee.entity.EmplEntity;
import com.estone.erp.erp_hr.biz.employee.service.EmplService;
import com.estone.erp.erp_hr.biz.performance.dingdingApi.DingUtil;
import com.estone.erp.erp_hr.biz.performance.dingdingApi.dto.DingMsgDTO;
import com.estone.erp.erp_hr.biz.sys.entity.DeptEntity;
import com.estone.erp.erp_hr.biz.sys.service.DeptService;
import com.estone.erp.erp_hr.util.TokenDataUtils;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 假期消费记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@RestController
@RequestMapping("/empLeaveTransactionRecords")
@Slf4j
public class EmpLeaveTransactionRecordsController {
    
    @Resource
    private EmpLeaveTransactionRecordsService empLeaveTransactionRecordsService;

    @Resource
    private EmplService emplService;

    @Resource
    private DeptService deptService;

    @Resource
    private EmpLeaveAdjustmentLogService empLeaveAdjustmentLogService;

    @Autowired
    private DingUtil dingUtil;

    @PostMapping("/list")
    public ApiResult<?> queryExpiredRecords(@RequestBody LeaveBalanceQueryDto queryParam) {
        try {
            LambdaQueryWrapper<EmpLeaveTransactionRecords> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(EmpLeaveTransactionRecords::getStatus,
                Arrays.asList(CompensatoryLeaveStatusEnum.EXPIRED.getCode(), CompensatoryLeaveStatusEnum.ABOUT_TO_EXPIRE.getCode()))
                .eq(EmpLeaveTransactionRecords::getLeaveRecordType, LeaveRecordTypeEnum.MODIFY_QUOTA.getCode())
                .orderByAsc(EmpLeaveTransactionRecords::getGmtCreate);
            
            Page<EmpLeaveTransactionRecords> page = new Page<>(queryParam.getCurrentPage(), queryParam.getPageSize());
            IPage<EmpLeaveTransactionRecords> recordsPage = empLeaveTransactionRecordsService.page(page, queryWrapper);
            if (CollectionUtils.isEmpty(recordsPage.getRecords())) {
                return ApiResult.newSuccess("没有过期记录");
            }
            // 获取员工信息
            List<String> userIds = recordsPage.getRecords().stream().map(EmpLeaveTransactionRecords::getUserId).collect(Collectors.toList());
            List<EmplEntity> emplList = emplService.list(new LambdaQueryWrapper<EmplEntity>().in(EmplEntity::getUserId, userIds));
            if (CollectionUtils.isEmpty(emplList)) {
                return ApiResult.newError("没有员工信息");
            }
            Map<String, EmplEntity> emplMap = emplList.stream().collect(Collectors.toMap(EmplEntity::getUserId, e -> e));
            // 获取部门信息
            List<Long> deptIds = emplList.stream().map(EmplEntity::getDeptId).collect(Collectors.toList());
            Map<Long, String> deptMap = deptService.getFirstDeptName(deptIds);
            List<EmpLeaveTransactionRecordsVo> voList = recordsPage.getRecords().stream()
                .map(record -> {
                    EmplEntity empl = emplMap.get(record.getUserId());
                    return new EmpLeaveTransactionRecordsVo(
                        record.getId(),
                        empl != null ? empl.getName() : null,
                        empl != null ? empl.getEmployeeNo() : null,
                        empl != null ? deptMap.get(empl.getDeptId()) : null,
                        record.getSurplusNumPerHourStr(),
                        record.getRecordNumPerHourStr(),
                        record.getExpireDate(),
                        record.getExpireStatusWithDayStr(),
                        record.getCreatedAt(),
                        record.getStatus()
                    );
                })
                .collect(Collectors.toList());
            Page<EmpLeaveTransactionRecordsVo> pageVo = new Page<>(queryParam.getCurrentPage(), queryParam.getPageSize());
            BeanUtils.copyProperties(recordsPage, pageVo);
            pageVo.setRecords(voList);
            pageVo.setTotal(recordsPage.getTotal());
            return ApiResult.newSuccess(pageVo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError("查询过期记录异常: " + e.getMessage());
        }
    }

    @PostMapping("/sendDingMessages")
    public ApiResult<?> sendDingMessages(@RequestBody List<Long> ids) {
        try {
            if (CollectionUtils.isEmpty(ids)) {
                return ApiResult.newError("ID列表不能为空");
            }

            List<EmpLeaveTransactionRecords> records = empLeaveTransactionRecordsService.listByIds(ids);
            if (CollectionUtils.isEmpty(records)) {
                return ApiResult.newError("未找到对应的假期记录");
            }

            // 获取员工信息
            List<String> userIds = records.stream().map(EmpLeaveTransactionRecords::getUserId).collect(Collectors.toList());
            List<EmplEntity> emplList = emplService.list(new LambdaQueryWrapper<EmplEntity>().in(EmplEntity::getUserId, userIds));
            if (CollectionUtils.isEmpty(emplList)) {
                return ApiResult.newError("没有员工信息");
            }
            Map<String, EmplEntity> emplMap = emplList.stream().collect(Collectors.toMap(EmplEntity::getUserId, e -> e));
            // 获取部门信息
            List<Long> deptIds = emplList.stream().map(EmplEntity::getDeptId).collect(Collectors.toList());
            Map<Long, String> deptMap  = deptService.getFirstDeptName(deptIds);
            String operatorId = TokenDataUtils.getEmployeeNo();
            String operatorName = TokenDataUtils.getEmployeeName();
            // 发送消息给每个员工
            List<EmpLeaveAdjustmentLog> logs = new ArrayList<>();
            for (EmpLeaveTransactionRecords record : records) {
                try {
                    String message = String.format("调休假期提醒：状态->%s, 剩余小时数->%s, 过期时间->%s",
                            CompensatoryLeaveStatusEnum.getStatusByCode(record.getStatus()),
                            record.getSurplusNumPerHourStr(),
                            record.getExpireDate() != null ? DateUtil.timeToString(record.getExpireDate()) : null);
                    DingMsgDTO dingMsgDTO = new DingMsgDTO();
                    dingMsgDTO.setUserIds(record.getUserId());
                    dingMsgDTO.setConent(message);
                    OapiMessageCorpconversationAsyncsendV2Response response = dingUtil.sendDingMessage(dingMsgDTO);
                    EmpLeaveAdjustmentLog log = new EmpLeaveAdjustmentLog();
                    log.setLogTime(new Date());
                    log.setEmployeeNo(emplMap.get(record.getUserId()).getEmployeeNo());
                    log.setEmployeeName(emplMap.get(record.getUserId()).getName());
                    log.setDepartment(deptMap.get(emplMap.get(record.getUserId()).getDeptId()));
                    if (record.getCreatedAt() != null)
                        log.setBatchNo(DateUtil.timeToString(record.getCreatedAt()));
                    log.setAdjustType(2); // 2表示发送消息
                    if (record.getCreatedAt() != null)
                        log.setBatchNo(DateUtil.timeToString(record.getCreatedAt()));
                    log.setOriginalIssueDate(record.getCreatedAt());
                    if (StringUtils.isNotBlank(record.getSurplusNumPerHourStr()))
                        log.setOriginalHours(BigDecimal.valueOf(Double.valueOf(record.getSurplusNumPerHourStr())));
                    log.setOriginalExpiryDate(record.getExpireDate());
                    log.setAdjustAction("即将过期："+record.getSurplusNumPerHourStr()+"小时");
                    log.setAdjustReason(message);
                    log.setOperatorType(2); // 1表示系统自动
                    log.setOperatorId(operatorId);
                    log.setOperatorName(operatorName);
                    log.setCreateTime(new Date());
                    log.setUpdateTime(new Date());
                    if (response != null && response.isSuccess()) {
                        log.setRemark("消息发送成功");
                    } else {
                        log.setRemark(response != null ? response.getErrmsg() : "发送失败");
                    }
                    logs.add(log);
                } catch (Exception e) {
                    log.error("发送消息给员工 {} 失败: {}", record.getUserId(), e.getMessage());
                }
            }
            empLeaveAdjustmentLogService.saveBatch(logs);
            return ApiResult.newSuccess("钉钉消息发送成功");
        } catch (Exception e) {
            return ApiResult.newError("钉钉消息发送异常: " + e.getMessage());
        }
    }

    @PostMapping("/batchClearLeaveRecords")
    public ApiResult<?> batchClearLeaveRecords(@RequestBody List<Long> ids) {
        try {
            if (CollectionUtils.isEmpty(ids)) {
                return ApiResult.newError("ID列表不能为空");
            }
            List<EmpLeaveTransactionRecords> records = empLeaveTransactionRecordsService.listByIds(ids);
            if (CollectionUtils.isEmpty(records)) {
                return ApiResult.newError("未找到对应的假期记录");
            }
            // 检查所有记录是否都是已过期状态
            if (!records.stream().allMatch(record ->
                    CompensatoryLeaveStatusEnum.EXPIRED.getCode()==(record.getStatus()))) {
                return ApiResult.newError("存在非过期状态的记录，无法执行清零操作");
            }
            // 获取员工信息
            List<String> userIds = records.stream().map(EmpLeaveTransactionRecords::getUserId).collect(Collectors.toList());
            List<EmplEntity> emplList = emplService.list(new LambdaQueryWrapper<EmplEntity>().in(EmplEntity::getUserId, userIds));
            if (CollectionUtils.isEmpty(emplList)) {
                return ApiResult.newError("没有员工信息");
            }
            Map<String, EmplEntity> emplMap = emplList.stream().collect(Collectors.toMap(EmplEntity::getUserId, e -> e));

            // 获取部门信息
            List<Long> deptIds = emplList.stream().map(EmplEntity::getDeptId).collect(Collectors.toList());
            List<DeptEntity> deptList = deptService.listByIds(deptIds);
            if (CollectionUtils.isEmpty(deptList)) {
                return ApiResult.newError("没有部门信息");
            }
            Map<Long, String> deptMap = deptList.stream().collect(Collectors.toMap(DeptEntity::getDeptId, DeptEntity::getName));
            // 调用服务层方法记录操作日志
            String operatorId = TokenDataUtils.getEmployeeNo();
            String operatorName = TokenDataUtils.getEmployeeName();
            Map<Long, String> failedRecords = new HashMap<>();
            for (EmpLeaveTransactionRecords record : records) {
                EmpLeaveAdjustmentLog empLeaveAdjustmentLog = new EmpLeaveAdjustmentLog();
                empLeaveAdjustmentLog.setLogTime(new Date());
                empLeaveAdjustmentLog.setEmployeeNo(record.getUserId());
                empLeaveAdjustmentLog.setEmployeeName(emplMap.get(record.getUserId()).getName());
                empLeaveAdjustmentLog.setDepartment(deptMap.get(emplMap.get(record.getUserId()).getDeptId()));
                if (record.getCreatedAt() != null)
                    empLeaveAdjustmentLog.setBatchNo(DateUtil.timeToString(record.getCreatedAt()));
                empLeaveAdjustmentLog.setOriginalIssueDate(record.getCreatedAt());
                empLeaveAdjustmentLog.setOriginalHours(BigDecimal.valueOf(record.getRecordNumPerHour()));
                empLeaveAdjustmentLog.setOriginalExpiryDate(record.getExpireDate());
                empLeaveAdjustmentLog.setAdjustType(1);
                empLeaveAdjustmentLog.setAdjustAction("清零"+record.getSurplusNumPerHour()+"小时");
                empLeaveAdjustmentLog.setAdjustReason("批量清零假期记录");
                empLeaveAdjustmentLog.setOperatorType(2); // 1表示系统自动
                empLeaveAdjustmentLog.setOperatorId(operatorId);
                empLeaveAdjustmentLog.setOperatorName(operatorName);
                empLeaveAdjustmentLog.setCreateTime(new Date());
                empLeaveAdjustmentLog.setUpdateTime(new Date());
                try {
                    empLeaveTransactionRecordsService.clearOperation(record);
                    empLeaveAdjustmentLog.setRemark("清零成功");
                } catch (Exception e) {
                    empLeaveAdjustmentLog.setRemark("清零失败");
                    failedRecords.put(record.getId(),e.getMessage());
                    log.error(e.getMessage(),e);
                }
                // 记录日志
                empLeaveAdjustmentLogService.save(empLeaveAdjustmentLog);
            }
            if (MapUtils.isEmpty(failedRecords)) {
                return ApiResult.newSuccess("批量清零成功");
            } else {
                return ApiResult.newError("部分失败: " + failedRecords);
            }
        } catch (Exception e) {
            return ApiResult.newError("批量清零异常: " + e.getMessage());
        }
    }

}

