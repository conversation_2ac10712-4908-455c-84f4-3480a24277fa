package com.estone.erp.erp_hr.biz.approve_n.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.estone.erp.common.mongo.model.CQuery;
import com.estone.erp.erp_hr.biz.approve_n.dto.ApprovalProcessDTO;
import com.estone.erp.erp_hr.biz.approve_n.dto.CanalApproveDTO;
import com.estone.erp.erp_hr.biz.approve_n.dto.DingDingResignApproveCallBackEventDTO;
import com.estone.erp.erp_hr.biz.approve_n.dto.LeaveApproveDTO;
import com.estone.erp.erp_hr.biz.approve_n.entity.ApprovalProcessEntity;
import com.estone.erp.erp_hr.biz.approve_n.entity.LeaveApprove;
import com.estone.erp.erp_hr.biz.approve_n.vo.LeaveApproveVO;
import com.estone.erp.erp_hr.biz.employee.entity.EmplEntity;

/**
 * <p>
 * 请假申请表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
public interface LeaveApproveService extends IService<LeaveApprove> {

    /**
     * 创建审批
     * 
     * @param processInstanceId
     * @param processInstance
     * @param emplEntity
     * @return
     */
    boolean createByProcessInstance(String processInstanceId,
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance, EmplEntity emplEntity);

    /**
     * 取消申请
     *
     * @param canalApproveDTO 取消申请DTO
     * @return 是否取消成功
     */
    boolean cancel(CanalApproveDTO canalApproveDTO);


    boolean process(String processInstanceId, OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance,
            EmplEntity emplEntity, DingDingResignApproveCallBackEventDTO dto, boolean isDing);

    /**
     * 请假详情
     *
     * @param id 申请ID
     * @return 请假详情
     */
    LeaveApproveVO detail(Long id);

    /***
     * @Description 钉钉审批结束回调（审批通过或审批拒绝）
     * @Date 2022/7/25 9:34
     * @param: processInstanceId：审批实例id
     * @param: result ：成功或失败
     * @return java.lang.Boolean
     **/
    Boolean dingDingFinish(String processInstanceId, String result);

    /**
     * 请假申请列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<LeaveApprove> listLeaveApprove(CQuery<LeaveApproveDTO> query);

    /**
     * 根据员工工号查询员工的请假申请记录
     * 
     * @param employeeNo 员工工号
     * @return 请假申请记录列表
     */
    List<LeaveApprove> getByEmployeeNo(String employeeNo);
}
