package com.estone.erp.erp_hr.biz.approve_n.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.estone.erp.common.redis.config.RedisClusterTemplate;
import com.estone.erp.common.redis.util.ErpUsermgtNRedisConStant;
import com.estone.erp.erp_hr.biz.approve_n.dto.*;
import com.estone.erp.erp_hr.biz.approve_n.enums.*;
import com.estone.erp.erp_hr.biz.attendance.constant.LeaveTypeEnum;
import com.estone.erp.erp_hr.biz.attendance.service.EmpLeaveBalanceService;
import com.estone.erp.erp_hr.common.constant.CommonConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dingtalk.api.response.OapiProcessinstanceExecuteV2Response;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.CErrorCode;
import com.estone.erp.common.mongo.model.CQuery;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.erp_hr.biz.approve_n.entity.ApprovalProcessEntity;
import com.estone.erp.erp_hr.biz.approve_n.entity.LeaveApprove;
import com.estone.erp.erp_hr.biz.approve_n.mapper.LeaveApproveMapper;
import com.estone.erp.erp_hr.biz.approve_n.service.ApprovalProcessService;
import com.estone.erp.erp_hr.biz.approve_n.service.LeaveApproveService;
import com.estone.erp.erp_hr.biz.approve_n.utils.BuildProcessUtil;
import com.estone.erp.erp_hr.biz.approve_n.vo.LeaveApproveVO;
import com.estone.erp.erp_hr.biz.employee.entity.EmplEntity;
import com.estone.erp.erp_hr.biz.employee.service.EmplService;
import com.estone.erp.erp_hr.biz.sys.enums.LogModuleEnum;
import com.estone.erp.erp_hr.biz.sys.enums.OperationTypeEnum;
import com.estone.erp.erp_hr.biz.sys.service.DeptService;
import com.estone.erp.erp_hr.biz.sys.utils.OperationLogUtils;
import com.estone.erp.erp_hr.biz.sys.vo.DeptVO;
import com.estone.erp.erp_hr.util.DataContextUtils;
import com.estone.erp.erp_hr.util.DingDingClientUtils;
import com.estone.erp.erp_hr.util.TokenDataUtils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 请假申请表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Slf4j
@Service("leaveApproveService")
public class LeaveApproveServiceImpl extends ServiceImpl<LeaveApproveMapper, LeaveApprove>
        implements LeaveApproveService {

    @Resource
    private ApprovalProcessService approvalProcessService;

    @Resource
    private EmplService emplService;
    @Resource
    private DeptService deptService;
    
    @Resource
    private EmpLeaveBalanceService empLeaveBalanceService;
    @Resource
    private RedisClusterTemplate redisClusterTemplate;

    @Override
    public IPage<LeaveApprove> listLeaveApprove(CQuery<LeaveApproveDTO> query) {
        LeaveApproveDTO params = query.getSearch();
        String employeeNo = DataContextUtils.getEmployeeNo();
        // 超级管理员不需要校验数据权限
        if (CollectionUtils.isNotEmpty(params.getPermissionEmployeeNos())
                && !CommonConstant.SUPER_ADMIN.equals(employeeNo)) {
            List<Long> idList = this.baseMapper.listApplicationId(params.getPermissionEmployeeNos());
            if (CollectionUtils.isEmpty(idList))
                return new Page<>();
            query.getSearch().setIdList(idList);
        }
        LambdaQueryWrapper<LeaveApprove> queryWrapper = handleWrapper(params);
        // 分页查询
        long current = query.getOffset();
        long size = query.getLimit();

        Page<LeaveApprove> page = new Page<>(current, size);
        // 将Entity转换为VO并返回IPage
        return this.page(page, queryWrapper);
    }

    private LambdaQueryWrapper<LeaveApprove> handleWrapper(LeaveApproveDTO params) {
        LambdaQueryWrapper<LeaveApprove> queryWrapper = Wrappers.lambdaQuery();
        // 默认按创建时间倒序
        queryWrapper.orderByDesc(LeaveApprove::getCreateDate);
        // 根据条件构建查询
        if (params == null)
            return queryWrapper;
        if (StrUtil.isNotBlank(params.getName())) {
            queryWrapper.like(LeaveApprove::getEmployeeName, params.getName());
        }

        if (ObjectUtil.isNotNull(params.getStatus())) {
            queryWrapper.eq(LeaveApprove::getStatus, params.getStatus());
        }
        if (ObjectUtil.isNotNull(params.getLeaveType())) {
            queryWrapper.eq(LeaveApprove::getLeaveType, params.getLeaveType());
        }
        // 状态集合筛选
        if (CollUtil.isNotEmpty(params.getStatusList())) {
            queryWrapper.in(LeaveApprove::getStatus, params.getStatusList());
        }

        if (CollUtil.isNotEmpty(params.getIdList())) {
            queryWrapper.in(LeaveApprove::getId, params.getIdList());
        }
        
        return queryWrapper;
    }

    @Override
    public boolean createByProcessInstance(String processInstanceId,
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance, EmplEntity emplEntity) {
        if (emplEntity == null || StringUtils.isBlank(emplEntity.getEmployeeNo())
                || StringUtils.isBlank(processInstanceId) || processInstance == null)
            return false;

        LeaveApprove dBApprove = baseMapper.selectOne(
                Wrappers.<LeaveApprove> lambdaQuery().eq(LeaveApprove::getProcessInstanceId, processInstanceId));
        if (dBApprove != null) {
            return true;
        }
        String employeeNo = emplEntity.getEmployeeNo();

        // 审批创建时间
        Date createTime = processInstance.getCreateTime();
        /** 审批流 */
        List<OapiProcessinstanceGetResponse.OperationRecordsVo> operationRecords = processInstance
                .getOperationRecords();
        if (CollectionUtils.isEmpty(operationRecords))
            return false;
        operationRecords.sort(Comparator.comparing(OapiProcessinstanceGetResponse.OperationRecordsVo::getDate));

        /** 审批任务节点 ，当创建审批在审批流会有自己的员工创建记录，在审批任务节点会有你下一个审批人（审批任务，也就是你申请后谁来审批） */
        List<OapiProcessinstanceGetResponse.TaskTopVo> tasks = processInstance.getTasks();
        if (CollectionUtils.isEmpty(tasks))
            return false;
        /**
         * 请假内容
         */
        List<OapiProcessinstanceGetResponse.FormComponentValueVo> componentValues = processInstance
                .getFormComponentValues();
        if (CollectionUtils.isEmpty(componentValues))
            return false;
        Map<String, OapiProcessinstanceGetResponse.FormComponentValueVo> componentValueMap = componentValues.stream()
                .collect(Collectors.toMap(OapiProcessinstanceGetResponse.FormComponentValueVo::getComponentType, v -> v,
                        (k1, k2) -> k2));

        OapiProcessinstanceGetResponse.TaskTopVo taskTopVo = tasks.stream()
                .filter(t -> StringUtils.equalsIgnoreCase(t.getTaskStatus(), "RUNNING")).findFirst().orElse(null);
        if (taskTopVo == null) {
            tasks.sort(Comparator.comparing(task -> Long.valueOf(task.getTaskid())));
            taskTopVo = tasks.get(0);
        }
        String approver = emplService.getByUserId(taskTopVo.getUserid());

        // 构建审批流
        List<ApprovalProcessEntity> approvalProcesss = new ArrayList<>();
        String applicationContent = ApplicationTypeEnum.LEAVE_APPLY.getName() + "(钉钉同步)";
        Integer applicationType = ApplicationTypeEnum.LEAVE_APPLY.getCode();
        ApprovalProcessEntity entity0 = BuildProcessUtil.buildApproveEntity(employeeNo, applicationContent,
                applicationType, createTime, employeeNo, processInstanceId, taskTopVo.getCreateTime());
        entity0.setStatus(ApproveStatusEnum.PASS.getCode());
        approvalProcesss.add(entity0);

        ApprovalProcessEntity entity = BuildProcessUtil.buildApproveEntity(employeeNo, applicationContent,
                applicationType, createTime, approver, processInstanceId, taskTopVo.getCreateTime());
        // taskId任务节点id
        entity.setTaskId(taskTopVo.getTaskid());
        // 审批流序号
        entity.setOrderNumber(entity0.getOrderNumber() + 1);
        entity.setProcessStep(ProcessStepEnum.DING_APPROVE.getCode());
        // 流程类型 1-申请 2-审批
        entity.setProcessType(ProcessTypeEnum.APPROVE.getCode());
        approvalProcesss.add(entity);

        Map<Long, DeptVO> deptVOMap = deptService.selectDeptID2Map();
        DeptVO deptVO = deptVOMap.get(emplEntity.getDeptId());
        Asserts.isFalse(deptVO == null, CErrorCode.REQUEST_PARAM_ERROR, "部门数据不存在");

        OapiProcessinstanceGetResponse.FormComponentValueVo textareaField = componentValueMap.get("TextareaField");
        OapiProcessinstanceGetResponse.FormComponentValueVo ddHolidayField = componentValueMap.get("DDHolidayField");

        LeaveApprove approveEntity = new LeaveApprove();
        approveEntity.setEmployeeNo(emplEntity.getEmployeeNo());
        approveEntity.setEmployeeName(emplEntity.getName());
        approveEntity.setDeptId(emplEntity.getDeptId());
        approveEntity.setPositionId(emplEntity.getPositionId());
        approveEntity.setSuperior(deptVO.getLeader());
        if (ddHolidayField != null && StringUtils.isNotBlank(ddHolidayField.getValue())) {
            JSONArray jsonArray = JSONObject.parseArray(ddHolidayField.getValue());

            if (jsonArray != null && !jsonArray.isEmpty() && jsonArray.size() > 5) {
                Double durationInHour = Double.valueOf(jsonArray.get(2).toString());
                String leaveUnit = jsonArray.get(3).toString();
                String tag = jsonArray.get(4).toString();
                approveEntity.setLeaveType(LeaveTypeEnum.getCodeByName(tag));
                approveEntity.setDuration(durationInHour);
                String startTime = jsonArray.get(0).toString();
                String endTime = jsonArray.get(1).toString();
                if (StringUtils.equalsIgnoreCase(leaveUnit, "day")) {
                    startTime = startTime + " 00:00:00";
                    endTime = endTime + " 00:00:00";
                    approveEntity.setLeaveUnit("天");
                }
                else {
                    startTime = startTime + ":00";
                    endTime = endTime + ":00";
                    approveEntity.setLeaveUnit("小时");
                }
                approveEntity.setStartDate(DateUtil.parse(startTime, DatePattern.NORM_DATETIME_PATTERN));
                approveEntity.setEndDate(DateUtil.parse(endTime, DatePattern.NORM_DATETIME_PATTERN));

            }
        }
        if (textareaField != null && StringUtils.isNotBlank(textareaField.getValue()))
            approveEntity.setLeaveReason(textareaField.getValue());
        approveEntity.setProcessInstanceId(processInstanceId);
        approveEntity.setStatus(ApproveStatusEnum.WAITTING.getCode());
        approveEntity.setCreateDate(createTime);
        approveEntity.setApprover(approver);
        approveEntity.setUpdator(TokenDataUtils.getEmployeeNo());
        approveEntity.setUpdatedDate(new Date());
        // 获取年假余额
        Double leaveBalance = empLeaveBalanceService.getLeaveBalanceByUserIdAndType(emplEntity.getUserId(),
                LeaveTypeEnum.ANNUAL_LEAVE.getLeaveCode());
        approveEntity.setLeaveBalance(leaveBalance);

        int result = baseMapper.insert(approveEntity);
        if (result > 0) {
            approvalProcesss.forEach(item -> item.setApplicationId(approveEntity.getId()));
            // 创建审批流
            approvalProcessService.saveBatch(approvalProcesss);
            OperationLogUtils.log(String.valueOf(entity.getId()), "新建请假审批(同步钉钉数据)", OperationTypeEnum.ADD,
                    LogModuleEnum.LEAVE_APPROVE);
        }
        return true;
    }

    /**
     * 创建审批流
     *
     * @param processInstanceId
     * @param taskTopVo
     * @param approver
     */
    private void createProcess(String processInstanceId, OapiProcessinstanceGetResponse.TaskTopVo taskTopVo,
            String approver) {
        List<ApprovalProcessEntity> approvalProcessEntitys = approvalProcessService
                .list(Wrappers.<ApprovalProcessEntity> lambdaQuery().eq(ApprovalProcessEntity::getProcessInstanceId,
                        processInstanceId));

        if (CollectionUtils.isEmpty(approvalProcessEntitys)) {
            return;
        }
        // tasks 审批节点会有重复的情况，我们只需要判断审批流里同一审批实例id并且有当前审批人，有则返回不做记录
        Optional<ApprovalProcessEntity> firstOptional = approvalProcessEntitys.stream().filter(
                item -> item.getApprover().equals(approver) && item.getProcessInstanceId().equals(processInstanceId))
                .findFirst();
        if (firstOptional.isPresent()) {
            log.info("当前task审批任务审批人在系统审批流已存在，审批实例id：{}，审批人工号：{}", processInstanceId, approver);
            return;
        }
        approvalProcessEntitys.sort(Comparator.comparing(ApprovalProcessEntity::getOrderNumber).reversed());
        ApprovalProcessEntity approvalProcessEntity = approvalProcessEntitys.get(0);
        ApprovalProcessEntity entity = BuildProcessUtil.buildApproveEntity(approvalProcessEntity.getApplicant(),
                approvalProcessEntity.getApplicationContent(), approvalProcessEntity.getApplicationType(),
                approvalProcessEntity.getApplicationDate(), approver, processInstanceId, taskTopVo.getCreateTime());
        entity.setApplicationId(approvalProcessEntity.getApplicationId());
        // taskId任务节点id,第一条没有
        entity.setTaskId(taskTopVo.getTaskid());
        // 审批流序号
        entity.setOrderNumber(approvalProcessEntity.getOrderNumber() + 1);
        entity.setProcessStep(ProcessStepEnum.DING_APPROVE.getCode());
        // 流程类型 1-申请 2-审批
        entity.setProcessType(ProcessTypeEnum.APPROVE.getCode());
        // 创建审批流
        approvalProcessService.save(entity);

        if (ApproveStatusEnum.PASS.getCode().equals(approvalProcessEntity.getStatus())) {
            LeaveApprove dbApprove = this.getById(approvalProcessEntity.getApplicationId());
            if (dbApprove != null && !StringUtils.equalsIgnoreCase(dbApprove.getApprover(), approver)) {
                LeaveApprove updateApprove = new LeaveApprove();
                updateApprove.setId(dbApprove.getId());
                updateApprove.setApprover(approver);
                this.updateById(updateApprove);
            }
        }
    }

    @Override
    public Boolean dingDingFinish(String processInstanceId, String result) {
        // 审批正常结束（同意或拒绝），是通过，那就去改变离职审批申请，拒绝不用处理，在审批任务的时候驳回已经处理
        if (DingDingEventTypeEnum.AGREE.getName().equals(result)) {
            LeaveApprove exist = this.getOne(
                    Wrappers.<LeaveApprove> lambdaQuery().eq(LeaveApprove::getProcessInstanceId, processInstanceId));
            if (exist != null) {
                LeaveApprove updateResign = new LeaveApprove();
                updateResign.setId(exist.getId());
                updateResign.setStatus(ApproveStatusEnum.PASS.getCode());
                updateResign.setCompleteDate(new Date());
                this.updateById(updateResign);
                // 将审批流状态改为通过
                approvalProcessService.update(Wrappers.<ApprovalProcessEntity> lambdaUpdate()
                        .eq(ApprovalProcessEntity::getProcessInstanceId, processInstanceId)
                        .set(ApprovalProcessEntity::getStatus, ApproveStatusEnum.PASS.getCode()));
                return true;
            }
            else {
                log.warn("===================审批正常结束（同意或拒绝）当前实例id下审批不存在系统~，ProcessInstanceId：{}", processInstanceId);
            }
        }
        return false;
    }

    @Override
    public LeaveApproveVO detail(Long id) {
        if (id == null) {
            return null;
        }

        LeaveApprove entity = this.getById(id);
        if (entity == null) {
            return null;
        }
        LeaveApproveVO vo = BeanUtil.copyProperties(entity, LeaveApproveVO.class);
        if (entity.getDeptId() != null)
            vo.setDeptFullName(redisClusterTemplate.hGet(ErpUsermgtNRedisConStant.GET_FULL_ORG_NAME_BY_ORG_ID,
                    String.class, String.valueOf(entity.getDeptId())));
        List<ApprovalProcessEntity> processEntityList = approvalProcessService
                .list(new QueryWrapper<ApprovalProcessEntity>().eq("application_id", id).eq("application_type",
                        ApplicationTypeEnum.LEAVE_APPLY.getCode()));
        Asserts.isFalse(CollectionUtils.isEmpty(processEntityList), CErrorCode.REQUEST_PARAM_ERROR, "审批流数据缺失");
        processEntityList.sort(Comparator.comparing(ApprovalProcessEntity::getOrderNumber));
        vo.setProcessList(processEntityList);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancel(CanalApproveDTO canalApproveDTO) {
        LeaveApprove leaveApprove = baseMapper.selectById(canalApproveDTO.getId());
        Asserts.isFalse(leaveApprove == null, CErrorCode.REQUEST_PARAM_ERROR, "数据不存在");
        if (ApproveStatusEnum.CANCEL.getCode().equals(leaveApprove.getStatus())) {
            return true;
        }
        // 钉钉操作不判断
        if (!canalApproveDTO.getSystemFlag()) {
            Asserts.isTrue(ApproveStatusEnum.WAITTING.getCode().equals(leaveApprove.getStatus()),
                    CErrorCode.REQUEST_PARAM_ERROR, "不是审批中不允许操作");
            Asserts.isTrue(leaveApprove.getEmployeeNo().equals(DataContextUtils.getEmployeeNo()),
                    CErrorCode.REQUEST_PARAM_ERROR, "不是本人不允许操作");
        }
        LeaveApprove updateApprove = new LeaveApprove();
        updateApprove.setId(leaveApprove.getId());
        updateApprove.setStatus(ApproveStatusEnum.CANCEL.getCode());
        int result = baseMapper.updateById(updateApprove);
        if (result > 0) {
            approvalProcessService.cancel(canalApproveDTO);
            OperationLogUtils.log(canalApproveDTO.getId().toString(), "取消申请", OperationTypeEnum.UPDATE,
                    LogModuleEnum.LEAVE_APPROVE);
        }

        return true;
    }


    @Override
    public boolean process(String processInstanceId,
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance, EmplEntity emplEntity,
            DingDingResignApproveCallBackEventDTO dto, boolean isDing) {

        if (emplEntity == null || StringUtils.isBlank(emplEntity.getEmployeeNo())
                || StringUtils.isBlank(processInstanceId) ||  dto == null)
            return false;

        String result = dto.getResult();
        String processContent = dto.getRemark();

        String type = dto.getType();
        
        LeaveApprove dBApprove = baseMapper.selectOne(
                Wrappers.<LeaveApprove> lambdaQuery().eq(LeaveApprove::getProcessInstanceId, processInstanceId));
        // 不是待审批的直接返回
        if (dBApprove == null || !ApproveStatusEnum.WAITTING.getCode().equals(dBApprove.getStatus()))
            return true;
        if (isDing){
            /** 审批流 */
            List<OapiProcessinstanceGetResponse.OperationRecordsVo> operationRecords = processInstance
                    .getOperationRecords();
            if (CollectionUtils.isEmpty(operationRecords))
                return false;
            operationRecords.sort(Comparator.comparing(OapiProcessinstanceGetResponse.OperationRecordsVo::getDate));

            /** 审批任务节点 ，当创建审批在审批流会有自己的员工创建记录，在审批任务节点会有你下一个审批人（审批任务，也就是你申请后谁来审批） */
            List<OapiProcessinstanceGetResponse.TaskTopVo> tasks = processInstance.getTasks();
            if (CollectionUtils.isEmpty(tasks))
                return false;

            OapiProcessinstanceGetResponse.TaskTopVo taskTopVo = tasks.stream()
                    .filter(t -> StringUtils.equalsIgnoreCase(t.getTaskStatus(), "RUNNING"))
                    .findFirst().orElse(null);
            if (taskTopVo == null) {
                return false;
            }
            String approver = emplService.getByUserId(taskTopVo.getUserid());
            // 构建下一个审批节点
            createProcess(processInstanceId, taskTopVo, approver);

            if (DingDingEventTypeEnum.START.getName().equals(type))
                return true;
        }

        List<ApprovalProcessEntity> approvalProcessEntitys = approvalProcessService
                .list(Wrappers.<ApprovalProcessEntity> lambdaQuery().eq(ApprovalProcessEntity::getProcessInstanceId,
                        processInstanceId));

        if (CollectionUtils.isEmpty(approvalProcessEntitys)) {
            return false;
        }
        ApprovalProcessEntity process = approvalProcessEntitys.stream()
                .filter(p -> p.getApprover().equals(emplEntity.getEmployeeNo())).findFirst().orElse(null);

        if (process == null || !ApproveStatusEnum.WAITTING.getCode().equals(process.getStatus())) {
            throw new RuntimeException("当前审批流为空或者不是待审批状态！");
        }
        process.setApproverUserId(emplEntity.getUserId());

        approvalProcessEntitys.sort(Comparator.comparing(ApprovalProcessEntity::getOrderNumber).reversed());
        ApprovalProcessEntity nexProcess = approvalProcessEntitys.get(0);
        LeaveApprove updateApprove = new LeaveApprove();
        updateApprove.setId(dBApprove.getId());
        updateApprove.setApprover(nexProcess.getApprover());// 更新为下一个审批人

        Integer status = ApproveStatusEnum.PASS.getCode();
        // 对于已经存在的审批流进行审批
        if (DingDingEventTypeEnum.REFUSE.getName().equals(result)) {
            // 拒绝
            status = ApproveStatusEnum.REJECT.getCode();
            updateApprove.setStatus(status);
            updateApprove.setCompleteDate(new Date());
        }

        // 驳回就结束
        if (isDing && ApproveStatusEnum.REJECT.getCode().equals(status)) {
            // 将审批流状态改为拒绝
            approvalProcessService.update(Wrappers.<ApprovalProcessEntity> lambdaUpdate()
                    .eq(ApprovalProcessEntity::getApplicationId, nexProcess.getApplicationId())
                    .eq(ApprovalProcessEntity::getApprover, emplEntity.getEmployeeNo())
                    .set(ApprovalProcessEntity::getStatus, ApproveStatusEnum.REJECT.getCode()));
            return true;
        }

        //修改当前节点状态
        ApprovalProcessEntity updateProcess = new ApprovalProcessEntity();
        updateProcess.setId(process.getId());
        updateProcess.setDingDing(process.isDingDing());
        updateProcess.setStatus(status);
        updateProcess.setProcessDate(new Date());
        approvalProcessService.updateById(updateProcess);

        int updated = baseMapper.updateById(updateApprove);
        if (!isDing && updated > 0) {
            // 调用钉钉审批接口
            doDingDingApprove(process, false, processContent, status);
            OperationLogUtils.log(dBApprove.getId().toString(),
                    "审批-" + ApproveStatusEnum.getNameByCode(process.getStatus()), OperationTypeEnum.UPDATE,
                    LogModuleEnum.LEAVE_APPROVE);
        }

        return true;
    }

    /**
     * 调用钉钉审批接口
     * 
     * @param process
     * @param isDing
     * @param processContent
     * @param status
     */
    private void doDingDingApprove(ApprovalProcessEntity process, boolean isDing, String processContent,
            Integer status) {
        if (process == null || status == null)
            return;

        String taskId = process.getTaskId();
        String approverUserId = process.getApproverUserId();
        String processInstanceId = process.getProcessInstanceId();
        // 调用钉钉审批接口,非钉钉回调、当前审批节点taskId不为空、当前审批节点人userId、钉钉审批实例id不为空
        if (!isDing && StrUtil.isNotBlank(taskId) && StrUtil.isNotBlank(approverUserId)
                && StrUtil.isNotBlank(processInstanceId)) {

            ExecuteApproveDTO executeApproveDTO = new ExecuteApproveDTO();
            executeApproveDTO.setTaskId(Long.parseLong(taskId));
            executeApproveDTO.setUserId(approverUserId);
            executeApproveDTO.setRemark(processContent);
            executeApproveDTO.setProcessInstanceId(processInstanceId);
            // 审批同意或拒绝
            if (ApproveStatusEnum.PASS.getCode().equals(status)) {
                executeApproveDTO.setResult(DingDingEventTypeEnum.AGREE.getName());
            }
            else if (ApproveStatusEnum.REJECT.getCode().equals(status)) {
                executeApproveDTO.setResult(DingDingEventTypeEnum.REFUSE.getName());
            }
            try {
                OapiProcessinstanceExecuteV2Response oapiProcessinstanceExecuteV2Response = DingDingClientUtils
                        .executeApproveDTO(executeApproveDTO);
                if (oapiProcessinstanceExecuteV2Response.isSuccess()) {
                    log.info("调用钉钉接口审批审批实例成功，参数：{}", JSON.toJSONString(executeApproveDTO));
                }
            }
            catch (Exception e) {
                log.error("调用钉钉接口审批审批实例失败,参数：{}，失败原因：{}", JSON.toJSONString(executeApproveDTO), e.getMessage());
                log.error("调用钉钉接口审批审批实例失败", e);
                throw new RuntimeException("调用钉钉接口审批审批实例失败" + JSON.toJSONString(executeApproveDTO) + e.getMessage());
            }
        }
        else {
            log.error("操作失败,发起钉钉审批参数错误！");
        }
    }

    @Override
    public List<LeaveApprove> getByEmployeeNo(String employeeNo) {
        if (StrUtil.isBlank(employeeNo)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<LeaveApprove> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(LeaveApprove::getEmployeeNo, employeeNo);
        queryWrapper.orderByDesc(LeaveApprove::getCreateDate);

        return this.list(queryWrapper);
    }
}
