package com.estone.erp.erp_hr.biz.attendance.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dingtalk.api.response.OapiAttendanceVacationQuotaListResponse;
import com.estone.erp.erp_hr.biz.attendance.constant.CompensatoryLeaveStatusEnum;
import com.estone.erp.erp_hr.biz.attendance.constant.LeaveRecordTypeEnum;
import com.estone.erp.erp_hr.biz.attendance.constant.LeaveTypeEnum;
import com.estone.erp.erp_hr.biz.attendance.dto.LeaveBalanceQueryDto;
import com.estone.erp.erp_hr.biz.attendance.entity.EmpLeaveBalance;
import com.estone.erp.erp_hr.biz.attendance.entity.EmpLeaveTransactionRecords;
import com.estone.erp.erp_hr.biz.attendance.mapper.EmpLeaveBalanceMapper;
import com.estone.erp.erp_hr.biz.attendance.service.EmpLeaveBalanceService;
import com.estone.erp.erp_hr.biz.attendance.service.EmpLeaveTransactionRecordsService;
import com.estone.erp.erp_hr.biz.employee.entity.EmplEntity;
import com.estone.erp.erp_hr.biz.employee.enums.EmplStatusEnum;
import com.estone.erp.erp_hr.biz.employee.service.EmplService;
import com.estone.erp.erp_hr.biz.sys.service.DeptService;
import com.estone.erp.erp_hr.biz.sys.utils.DeptUtils;
import com.estone.erp.erp_hr.biz.sys.vo.DeptVO;
import com.estone.erp.erp_hr.util.DingDingClientUtils;
import com.estone.erp.erp_hr.util.EasyExcelUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 员工假期额度总览表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Service
public class EmpLeaveBalanceServiceImpl extends ServiceImpl<EmpLeaveBalanceMapper, EmpLeaveBalance> implements EmpLeaveBalanceService {



    @Resource
    private EmplService emplService;

    @Resource
    private DeptService deptService;

    @Resource
    private EmpLeaveTransactionRecordsService empLeaveTransactionRecordsService;

    public final Long BATCH_NUM =  50L;

    /**
     * 查询员工假期余额
     * @param queryParam 查询参数对象
     * @return 员工假期余额列表
     */

    public Page<EmpLeaveBalance> queryLeaveBalance(LeaveBalanceQueryDto queryParam) {
        LambdaQueryWrapper<EmpLeaveBalance> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(queryParam.getEmployeeNo())) {
            queryWrapper.eq(EmpLeaveBalance::getEmployeeNo, queryParam.getEmployeeNo());
        }
        if (queryParam.getStatus() != null) {
            queryWrapper.eq(EmpLeaveBalance::getStatus, queryParam.getStatus());
        }

        if (CollectionUtils.isNotEmpty(queryParam.getIds())) {
            queryWrapper.in(EmpLeaveBalance::getId, queryParam.getIds());
        }
        Map<Long, DeptVO> deptVOMap = deptService.selectDeptID2Map();

        // 关联empl表查询，只查询在职状态的员工
        LambdaQueryWrapper<EmplEntity> emplQuery = new LambdaQueryWrapper<>();
       // emplQuery.in(EmplEntity::getStatus, EmplStatusEnum.getAllNoDimissionStatus());
        if (queryParam.getDeptId() != null) {
            List<Long> list = new ArrayList<>();
            list.add(queryParam.getDeptId());
            DeptUtils.getAllChidrenDeptIds(queryParam.getDeptId(), deptVOMap, list);
            emplQuery.in(CollectionUtils.isNotEmpty(list) , EmplEntity::getDeptId, list);
        }
        if (StringUtils.isNotBlank(queryParam.getName())) {
            emplQuery.like(EmplEntity::getName, queryParam.getName());
        }

        List<EmplEntity> emplList = emplService.list(emplQuery);
        Map<String, EmplEntity> nameMap = emplList.stream()
                .collect(Collectors.toMap(EmplEntity::getEmployeeNo, e -> e));
        Map<Long, String> firstDeptNameMap = deptService.getFirstDeptName(deptVOMap,emplList.stream().map(EmplEntity::getDeptId).collect(Collectors.toList()));

       /* List<DeptEntity> deptEntities = deptService.listByIds(emplList.stream().map(EmplEntity::getDeptId).collect(Collectors.toList()));
        Map<Long, String> deptMap = deptEntities.stream()
                .collect(Collectors.toMap(DeptEntity::getDeptId, DeptEntity::getName));*/
        if  (StringUtils.isNotBlank(queryParam.getEmployeeNo()) || queryParam.getDeptId() != null || StringUtils.isNotBlank(queryParam.getName())) {
            List<String> employeeNos = emplList.stream()
                    .map(EmplEntity::getEmployeeNo)
                    .collect(Collectors.toList());
            queryWrapper.in(EmpLeaveBalance::getEmployeeNo, employeeNos);
        }
        queryWrapper.orderByAsc(EmpLeaveBalance::getId);
        Page<EmpLeaveBalance> page = new Page<>();
        if (queryParam.getPageRequired() != null && !queryParam.getPageRequired()) {
            if (CollectionUtils.isNotEmpty(emplList)) {
                List<EmpLeaveBalance> balances = this.list(queryWrapper);;
                page.setRecords(balances);
                page.setTotal(balances.size());
            }
        } else {
            page = new Page<>(queryParam.getCurrentPage(), queryParam.getPageSize());
            if (CollectionUtils.isNotEmpty(emplList)) {

                // 将EmplEntity的name赋给EmpLeaveBalance的employeeName属性
                page = this.page(page, queryWrapper);
            }
        }
        page.getRecords().forEach(balance -> {
            EmplEntity emplEntity = nameMap.get(balance.getEmployeeNo());
            if (emplEntity != null) {
                balance.setEmployeeName(emplEntity.getName());
                balance.setDepartment(firstDeptNameMap.get(emplEntity.getDeptId()));
            }
        });
        return page;
    }

    /**
     * 查询所有在职用户，50 个为一个批次同步假期余额
     * @return 同步结果
     */
    public boolean syncAllActiveUsersLeaveBalance(List<String> empNoList) {
        boolean allSuccess = true;
        int batchSize = 20;

        // 使用 MyBatis-Plus 查询构造器直接查询所有在职用户
        LambdaQueryWrapper<EmplEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EmplEntity::getStatus, EmplStatusEnum.getAllNoDimissionStatus());
        if (CollectionUtils.isNotEmpty(empNoList))
            queryWrapper.in(EmplEntity::getEmployeeNo, empNoList);
        List<EmplEntity> allActiveUsers = emplService.list(queryWrapper);

        if (CollectionUtils.isEmpty(allActiveUsers)) {
            return allSuccess;
        }

        for (int i = 0; i < allActiveUsers.size(); i += batchSize) {
            // 获取当前批次的用户
            List<EmplEntity> batchUsers = allActiveUsers.subList(i, Math.min(i + batchSize, allActiveUsers.size()));

            // 拼接当前批次用户的 ID
            List<String> userIds = batchUsers.stream()
                    .map(EmplEntity::getUserId)
                    .collect(Collectors.toList());
            String userIdsStr = String.join(",", userIds);

            try {
                if(!syncLeaveBalance(userIdsStr,batchUsers)) {
                    allSuccess = false;
                }
            } catch (Exception e) {
                allSuccess = false;
                // 记录异常日志
                log.error("同步在职用户假期余额失败，用户 ID: "+userIdsStr, e);
            }
        }

        return allSuccess;
    }

    public boolean syncLeaveBalance(String userids,List<EmplEntity> activeUsers) {
        try {
            OapiAttendanceVacationQuotaListResponse response = DingDingClientUtils.getLeaveBalanceFromAPI(userids, LeaveTypeEnum.COMPENSATORY_LEAVE.getLeaveCode(),
                    BATCH_NUM);
            if (response != null && response.isSuccess() && response.getErrcode() == 0) {
                List<OapiAttendanceVacationQuotaListResponse.Leavequotas> quotas = response.getResult().getLeaveQuotas();
                if (quotas != null && !quotas.isEmpty()) {
                    return saveLeaveBalances(quotas,activeUsers);
                }
            }
            return false;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return false;
        }
    }

    public boolean saveLeaveBalances(List<OapiAttendanceVacationQuotaListResponse.Leavequotas> quotas, List<EmplEntity> activeUsers) {
        // 将 activeUsers 转换为以用户 ID 为键的 Map，方便查找
        Map<String, EmplEntity> userMap = activeUsers.stream()
                .collect(Collectors.toMap(EmplEntity::getUserId, user -> user));

        // 按 userId 分组汇总 QuotaNumPerHour 和 UsedNumPerHour
        Map<String, Map<String, BigDecimal>> userQuotaSummary = quotas.stream()
                .collect(Collectors.groupingBy(
                        OapiAttendanceVacationQuotaListResponse.Leavequotas::getUserid,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> {
                                    BigDecimal totalQuotaNumPerHour = list.stream()
                                            .map(quota -> quota.getQuotaNumPerHour() != null ? BigDecimal.valueOf(quota.getQuotaNumPerHour()) : BigDecimal.ZERO)
                                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                                    BigDecimal totalUsedNumPerHour = list.stream()
                                            .map(quota -> quota.getUsedNumPerHour() != null ? BigDecimal.valueOf(quota.getUsedNumPerHour()) : BigDecimal.ZERO)
                                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                                    Map<String, BigDecimal> summary = new HashMap<>();
                                    summary.put("totalQuotaNumPerHour", totalQuotaNumPerHour);
                                    summary.put("totalUsedNumPerHour", totalUsedNumPerHour);
                                    return summary;
                                }
                        )
                ));

        List<EmpLeaveBalance> balances = userQuotaSummary.entrySet().stream().map(entry -> {
            String userId = entry.getKey();
            Map<String, BigDecimal> summary = entry.getValue();
            BigDecimal totalQuotaNumPerHour = summary.get("totalQuotaNumPerHour");
            BigDecimal totalUsedNumPerHour = summary.get("totalUsedNumPerHour");

            EmpLeaveBalance balance = new EmpLeaveBalance();
            // 假设取 quotas 中该 userId 的第一条记录的其他属性，可根据实际需求调整
            Optional<OapiAttendanceVacationQuotaListResponse.Leavequotas> firstQuota = quotas.stream()
                    .filter(quota -> userId.equals(quota.getUserid()))
                    .findFirst();
            if (firstQuota.isPresent()) {
                OapiAttendanceVacationQuotaListResponse.Leavequotas quota = firstQuota.get();
                balance.setLeaveCode(quota.getLeaveCode());
                balance.setQuotaCycle(quota.getQuotaCycle());
                balance.setQuotaId(quota.getQuotaId());
                balance.setQuotaNumPerDay(quota.getQuotaNumPerDay());
                balance.setUsedNumPerDay(quota.getUsedNumPerDay());
                balance.setStartTime(quota.getStartTime());
                balance.setEndTime(quota.getEndTime());
                balance.setLastUpdated(new Date());
            }
            balance.setUserId(userId);
            balance.setQuotaNumPerHour(totalQuotaNumPerHour.longValue());
            balance.setUsedNumPerHour(totalUsedNumPerHour.longValue());
            // 计算 totalHours
            //balance.setTotalHours(totalQuotaNumPerHour.subtract(totalUsedNumPerHour));
            // 根据用户 ID 从 userMap 中获取对应的 EmplEntity 对象
            EmplEntity user = userMap.get(userId);
            if (user != null) {
                // 假设 EmplEntity 有 employeeName 和 department 等属性，根据实际情况修改
               // balance.setEmployeeName(user.getEmployeeName());
                balance.setEmployeeNo(user.getEmployeeNo());
            }
            return balance;
        }).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(balances)){
            return false;
        }
        
        // 根据userId查询已存在的记录
        List<String> userIds = balances.stream().map(EmpLeaveBalance::getUserId).collect(Collectors.toList());
        Map<String, EmpLeaveBalance> existBalances = this.list(new LambdaQueryWrapper<EmpLeaveBalance>().in(EmpLeaveBalance::getUserId, userIds))
            .stream().collect(Collectors.toMap(EmpLeaveBalance::getUserId, balance -> balance));
        
        // 分离需要更新和插入的记录
        List<EmpLeaveBalance> toUpdate = new ArrayList<>();
        List<EmpLeaveBalance> toInsert = new ArrayList<>();
        
        for(EmpLeaveBalance balance : balances) {
            EmpLeaveBalance exist = existBalances.get(balance.getUserId());
            if(exist != null) {
                // 更新已有记录
                balance.setId(exist.getId());
                toUpdate.add(balance);
            } else {
                // 插入新记录
                toInsert.add(balance);
            }
        }
        
        // 批量更新和插入
        boolean updateResult = true;
        if(!toUpdate.isEmpty()) {
            updateResult = this.updateBatchById(toUpdate);
        }
        boolean insertResult = true;
        if(!toInsert.isEmpty()) {
            insertResult = this.saveBatch(toInsert);
        }
        return updateResult && insertResult;
    }

    /**
     * 保存员工假期余额变动，并根据 activeUsers 完善 EmpLeaveBalance 对象的属性
     * @param quotas 假期额度列表
     * @param activeUsers 在职用户列表
     * @return 保存结果
     */
    public boolean syncAllEmpLeaveTransactionRecords(List<String> empNoList) {
        boolean allSuccess = true;
        int batchSize = 20;

        // 使用 MyBatis-Plus 查询构造器直接查询所有在职用户
        // 从 EmpLeaveBalance 表查询所有存在的用户 ID
        List<String> userIds = this.list(
                        new LambdaQueryWrapper<EmpLeaveBalance>().in(CollectionUtils.isNotEmpty(empNoList), EmpLeaveBalance::getEmployeeNo, empNoList)
                                .select(EmpLeaveBalance::getUserId)
                ).stream()
                .map(EmpLeaveBalance::getUserId)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(userIds)) {
            return allSuccess;
        }
        for (int i = 0; i < userIds.size(); i += batchSize) {
            // 获取当前批次的用户
            List<String> batchUsers = userIds.subList(i, Math.min(i + batchSize, userIds.size()));
            String userIdsStr = String.join(",", batchUsers);
            try {
                empLeaveTransactionRecordsService.syncLeaveTransactionRecords(userIdsStr);
            } catch (Exception e) {
                allSuccess = false;
                // 记录异常日志
                log.error("同步在职用户假期余额失败，用户 ID: "+userIdsStr, e);
            }
        }
        return allSuccess;
    }


    /**
     * 计算并更新剩余假期数量
     * @return 更新结果
     */
    public boolean calculateAndUpdateSurplusHours(List<String> empNoList) {
        // 查询所有quotaNumPerHour-usedNumPerHour>0的记录
        LambdaQueryWrapper<EmpLeaveBalance> balanceQuery = new LambdaQueryWrapper<>();
        balanceQuery.in(CollectionUtils.isNotEmpty(empNoList), EmpLeaveBalance::getEmployeeNo, empNoList);
       /* balanceQuery.gt(EmpLeaveBalance::getQuotaNumPerHour, 0)
                .in(CollectionUtils.isNotEmpty(empNoList), EmpLeaveBalance::getEmployeeNo, empNoList)
                .gt(EmpLeaveBalance::getTotalHours, 0);*/
        List<EmpLeaveBalance> balances = this.list(balanceQuery);

        if (CollectionUtils.isEmpty(balances)) {
            return true;
        }

        for (EmpLeaveBalance balance : balances) {
            // 逐个处理每个用户的记录
            List<EmpLeaveTransactionRecords> allRecords = new ArrayList<>();
            String userId = balance.getUserId();

            // 查询当前用户的MODIFY_QUOTA类型的记录并按gmtModified倒序
            LambdaQueryWrapper<EmpLeaveTransactionRecords> recordsQuery = new LambdaQueryWrapper<>();
            recordsQuery.eq(EmpLeaveTransactionRecords::getLeaveRecordType, LeaveRecordTypeEnum.MODIFY_QUOTA.getCode())
                    .eq(EmpLeaveTransactionRecords::getUserId, userId)
                    .orderByDesc(EmpLeaveTransactionRecords::getGmtModified);
            List<EmpLeaveTransactionRecords> userRecords = empLeaveTransactionRecordsService.list(recordsQuery);

            // 分配剩余小时数
            long totalSurplusNumPerHour = balance.getQuotaNumPerHour() - balance.getUsedNumPerHour();

            // 初始化状态为NORMAL
            Integer status = CompensatoryLeaveStatusEnum.NORMAL.getCode();

            for (EmpLeaveTransactionRecords record : userRecords) {
                if (totalSurplusNumPerHour <= 0) {
                    record.setSurplusNumPerHour(0L);
                    record.setStatus(CompensatoryLeaveStatusEnum.NORMAL.getCode());
                } else if (totalSurplusNumPerHour > record.getRecordNumPerHour()) {
                    record.setSurplusNumPerHour(record.getRecordNumPerHour());
                    totalSurplusNumPerHour -= record.getRecordNumPerHour();
                } else {
                    record.setSurplusNumPerHour(totalSurplusNumPerHour);
                    totalSurplusNumPerHour = 0;
                }
                // 设置状态
                if (record.getSurplusNumPerHour() <= 0) {
                    record.setStatus(CompensatoryLeaveStatusEnum.NORMAL.getCode());
                } else {
                    Date now = new Date();
                    Date expireDate = record.getExpireDate();
                    // 记录最早过期时间
                    if (expireDate != null) {
                        if (balance.getEarliestExpiryDate() == null || expireDate.before(balance.getEarliestExpiryDate())) {
                            balance.setEarliestExpiryDate(expireDate);
                        }
                        long diffInDays = (expireDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);
                        if (expireDate.compareTo(now) <= 0) {
                            record.setStatus(CompensatoryLeaveStatusEnum.EXPIRED.getCode());
                            // 过期状态优先级最高
                            status = CompensatoryLeaveStatusEnum.EXPIRED.getCode();
                        } else if (diffInDays <= 30 && status != CompensatoryLeaveStatusEnum.EXPIRED.getCode()) {
                            record.setStatus(CompensatoryLeaveStatusEnum.ABOUT_TO_EXPIRE.getCode());
                            // 即将过期状态次之
                            status = CompensatoryLeaveStatusEnum.ABOUT_TO_EXPIRE.getCode();
                        } else if (status != CompensatoryLeaveStatusEnum.EXPIRED.getCode() &&
                                status != CompensatoryLeaveStatusEnum.ABOUT_TO_EXPIRE.getCode()) {
                            record.setStatus(CompensatoryLeaveStatusEnum.NORMAL.getCode());
                        }
                    }
                }
                record.setCreatedAt(null);
                record.setUpdatedAt(null);
                record.setCalculatedHours(null);
                allRecords.add(record);
            }
            // 更新EmpLeaveBalance的状态
            this.update(new LambdaUpdateWrapper<EmpLeaveBalance>()
                .eq(EmpLeaveBalance::getId, balance.getId())
                .set(EmpLeaveBalance::getStatus, status)
                    .set(EmpLeaveBalance::getEarliestExpiryDate, balance.getEarliestExpiryDate()));
            empLeaveTransactionRecordsService.updateBatchById(allRecords);
        }
        return true;
    }

    /**
     * 获取特定用户特定假期类型的余额
     *
     * @param userId    钉钉用户ID
     * @param leaveType 假期类型代码
     * @return 假期余额（小时数），如果没有找到返回0.0
     */
    @Override
    public Double getLeaveBalanceByUserIdAndType(String userId, String leaveType) {
        try {
            // 直接调用API获取最新数据，一次获取50条记录
            OapiAttendanceVacationQuotaListResponse response = DingDingClientUtils.getLeaveBalanceFromAPI(userId, leaveType, BATCH_NUM);
            if (response == null || !response.isSuccess() || response.getErrcode() != 0) {
                return 0.0;
            }

            List<OapiAttendanceVacationQuotaListResponse.Leavequotas> quotas = response.getResult().getLeaveQuotas();
            if (CollectionUtils.isEmpty(quotas)) {
                return 0.0;
            }

            // 过滤出与当前用户相关的配额记录
            List<OapiAttendanceVacationQuotaListResponse.Leavequotas> userQuotas = quotas.stream()
                .filter(quota -> userId.equals(quota.getUserid()))
                .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(userQuotas)) {
                return 0.0;
            }

            // 计算总的配额和已使用小时数
            double totalQuotaHours = userQuotas.stream()
                .mapToDouble(quota -> quota.getQuotaNumPerHour() != null ? quota.getQuotaNumPerHour() : 0)
                .sum();

            double totalUsedHours = userQuotas.stream()
                .mapToDouble(quota -> quota.getUsedNumPerHour() != null ? quota.getUsedNumPerHour() : 0)
                .sum();

            // 计算总的配额和已使用天数
            double totalQuotaDays = userQuotas.stream()
                    .mapToDouble(quota -> quota.getQuotaNumPerDay() != null ? quota.getQuotaNumPerDay() : 0)
                    .sum();

            double totalUsedDays = userQuotas.stream()
                    .mapToDouble(quota -> quota.getUsedNumPerDay() != null ? quota.getUsedNumPerDay() : 0)
                    .sum();

            //假期类型按小时，计算该值不为空且按百分之一小时折算。例如：1000=10小时
            double hours = Math.max(0, totalQuotaHours - totalUsedHours) / 100;
            //假期类型按天计算时，该值不为空且按百分之一天折算。例如：100=1天。
            double days = Math.max(0, totalQuotaDays - totalUsedDays) / 100;
            // 返回可用余额
            return Math.max(hours, days);

        } catch (Exception e) {
            log.error("获取用户假期余额失败，用户ID: " + userId + ", 假期类型: " + leaveType, e);
            return 0.0;
        }
    }

    /**
     * 导出员工假期余额
     * @param response 响应对象
     * @param queryParam 查询参数
     * */
    @Override
    public void export(HttpServletResponse response, LeaveBalanceQueryDto queryParam) {
        Page<EmpLeaveBalance> empLeaveBalancePage = this.queryLeaveBalance(queryParam);
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = "员工假期余额" + DateUtil.date().toDateStr();
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcelUtils.writeExcel(response, empLeaveBalancePage.getRecords(), fileName, "sheet1", EmpLeaveBalance.class);
        } catch (Exception e) {
            log.error("导出员工假期余额失败：{}", e);
            throw new RuntimeException("导出员工假期余额失败,失败原因：" + e.getMessage());
        }

        }

}
