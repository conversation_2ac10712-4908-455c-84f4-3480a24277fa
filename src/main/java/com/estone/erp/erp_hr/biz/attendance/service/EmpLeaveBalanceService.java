package com.estone.erp.erp_hr.biz.attendance.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dingtalk.api.response.OapiAttendanceVacationQuotaListResponse;
import com.estone.erp.erp_hr.biz.attendance.dto.LeaveBalanceQueryDto;
import com.estone.erp.erp_hr.biz.attendance.entity.EmpLeaveBalance;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.erp_hr.biz.employee.entity.EmplEntity;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 员工假期额度总览表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
public interface EmpLeaveBalanceService extends IService<EmpLeaveBalance> {

    /**
     * 查询员工假期余额
     * @param queryParam 查询参数对象
     * @return 员工假期余额列表
     */
    Page<EmpLeaveBalance> queryLeaveBalance(LeaveBalanceQueryDto queryParam);

    boolean syncAllActiveUsersLeaveBalance(List<String> empNoList);

    /**
     * 同步员工假期余额
     * @param accessToken 访问令牌
     * @param linuxCode 假期类型代码
     * @param qgUserId 查询用户ID
     * @param userIds 用户ID列表
     * @return 同步结果
     */
    boolean syncLeaveBalance(String userIds,List<EmplEntity> activeUsers);

    /**
     * 保存员工假期余额
     * @param empLeaveBalance 假期余额实体
     * @return 保存结果
     */
    boolean saveLeaveBalances(List<OapiAttendanceVacationQuotaListResponse.Leavequotas> quotas,List<EmplEntity> activeUsers);

    /**
     * 同步所有员工的假期变动记录
     * @param empNoList 员工编号列表
     * @return 同步结果
     */
    boolean syncAllEmpLeaveTransactionRecords(List<String> empNoList);

    Double getLeaveBalanceByUserIdAndType(String userId, String leaveType);
    boolean calculateAndUpdateSurplusHours(List<String> empNoList);

    void export(HttpServletResponse response, LeaveBalanceQueryDto queryParam);
}
