package com.estone.erp.erp_hr.biz.employee.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.erp_hr.biz.employee.entity.ServicePlatformEntity;
import com.estone.erp.erp_hr.biz.employee.service.ServicePlatformService;
import com.estone.erp.erp_hr.client.feign.OmsClient;
import com.estone.erp.erp_hr.common.PageParams;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * 服务平台管理
 * <AUTHOR>
 * @date 2021-08-05 17:31
 */
@RestController
@RequestMapping("/servicePlatform")
@Slf4j
@AllArgsConstructor
public class ServicePlatformController {

    private final ServicePlatformService servicePlatformService;

    @Resource
    private OmsClient omsClient;
    @GetMapping("/getAll")
    public ApiResult<?> getAllServicePlatform() {
        List<ServicePlatformEntity> list = servicePlatformService.list();
        ApiResult<List<String>> saleChannels = omsClient.getSaleChannels();
        if (!saleChannels.isSuccess()) {
            log.info("获取订单系统服务平台失败~");
            return ApiResult.newError("获取订单系统服务平台失败");
        }
        List<String> saleChannelList = saleChannels.getResult();
        // 过滤出交集部分,并根据name去重复的

        List<ServicePlatformEntity> intersection = list.stream()
                .filter(platform -> saleChannelList.contains(platform.getName())
                        || StringUtils.contains(platform.getName(),"全平台"))
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                                new TreeSet<>(Comparator.comparing(ServicePlatformEntity::getName))),
                        ArrayList::new));

        return ApiResult.newSuccess(intersection);
    }

    @PostMapping(value = "/page")
    public ApiResult<?> page(@RequestBody PageParams<ServicePlatformEntity> pageParams) {
        IPage iPage = pageParams.buildPage();
        ServicePlatformEntity model = pageParams.getModel();
        return ApiResult.newSuccess(servicePlatformService.myPage(iPage,model));
    }

    @GetMapping("/add/{name}")
    public ApiResult<?> add(@PathVariable(value = "name")String name) {
        ServicePlatformEntity servicePlatformEntity = new ServicePlatformEntity();
        servicePlatformEntity.setName(name);
        return ApiResult.newSuccess(servicePlatformService.save(servicePlatformEntity));
    }

    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody ServicePlatformEntity servicePlatformEntity) {
        if (servicePlatformEntity == null || servicePlatformEntity.getId() == null || StrUtil.isBlank(servicePlatformEntity.getName())){
            ApiResult.newError("参数不能为空~");
        }
        return ApiResult.newSuccess(servicePlatformService.updateById(servicePlatformEntity));
    }

}
