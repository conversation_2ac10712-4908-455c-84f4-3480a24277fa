package com.estone.erp.erp_hr.biz.approve_n.controller;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.*;
import com.estone.erp.common.mongo.model.CQuery;
import com.estone.erp.common.mongo.model.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.erp_hr.biz.approve_n.dto.ApprovalProcessDTO;
import com.estone.erp.erp_hr.biz.approve_n.entity.ApprovalProcessEntity;
import com.estone.erp.erp_hr.biz.approve_n.enums.ApplicationTypeEnum;
import com.estone.erp.erp_hr.biz.approve_n.enums.ApproveStatusEnum;
import com.estone.erp.erp_hr.biz.approve_n.enums.ProcessTypeEnum;
import com.estone.erp.erp_hr.biz.approve_n.service.ApprovalProcessService;
import com.estone.erp.erp_hr.biz.approve_n.service.RegularApproveService;
import com.estone.erp.erp_hr.biz.approve_n.service.ResignApproveService;
import com.estone.erp.erp_hr.biz.approve_n.service.TransferApproveService;
import com.estone.erp.erp_hr.biz.approve_n.vo.RegularApproveVO;
import com.estone.erp.erp_hr.biz.approve_n.vo.ResignApproveVO;
import com.estone.erp.erp_hr.biz.approve_n.vo.TransferApproveVO;
import com.estone.erp.erp_hr.common.constant.CommonConstant;
import com.estone.erp.erp_hr.util.DataContextUtils;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 审批流程
 *
 * @ClassName: ApprovalProcess
 * <AUTHOR>
 * @Date 2021-02-20 15:21:26
 * @Version: 0.0.1
 */
@RestController
@RequestMapping("/approvalprocess")
@Slf4j
public class ApprovalProcessController {

    @Autowired
    private ApprovalProcessService approvalProcessService;

    @Autowired
    private RegularApproveService regularApproveService;

    @Autowired
    private TransferApproveService transferApproveService;

    @Autowired
    private ResignApproveService resignApproveService;

    /**
     * 我的申请/审批--列表查询
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/listApprovalProcess")
    @ApiOperation("我的申请/审批-列表查询")
    public ApiResult<?> listApprovalProcess(@RequestBody() @ApiParam(name = "param", value = "页面输入参数模型", required = true) ApiRequestParam<CQuery<ApprovalProcessDTO>> param) {
        CQuery<ApprovalProcessDTO> query = param.getArgs();
        if (query.getSearch() == null) {
            query.setSearch(new ApprovalProcessDTO());
        }
        ApprovalProcessDTO search = query.getSearch();
        String employeeNo = DataContextUtils.getEmployeeNo();
        if (StringUtils.isBlank(employeeNo) || CommonConstant.SYSTEM_CREATOR.equals(employeeNo)) {
            return ApiResult.newError("申请人或者审批人不能为空");
        }
        Asserts.isFalse(search.getProcessType() == null, CErrorCode.REQUEST_PARAM_ERROR, "流程类型不能为空");
        if (ProcessTypeEnum.APPLICATION.getCode().equals(search.getProcessType())) {
            // 申请
            search.setApplicant(employeeNo);
        } else if (ProcessTypeEnum.APPROVE.getCode().equals(search.getProcessType())) {
            // 审批
            search.setApprover(employeeNo);
        } else {
            return ApiResult.newError("流程类型错误");
        }
        //排除请假申请
        search.setNotExistsApplicationTypeList(Collections.singletonList(ApplicationTypeEnum.LEAVE_APPLY.getCode()));

        search.setStatusList(Arrays.asList(ApproveStatusEnum.WAITTING.getCode(), ApproveStatusEnum.PASS.getCode(), ApproveStatusEnum.CANCEL.getCode(), ApproveStatusEnum.REJECT.getCode()));
        CQueryResult<ApprovalProcessEntity> result = approvalProcessService.listApprovalProcess(query);
        return result;
    }

    /**
     * 申请详情
     *
     * @param applicationType
     * @param applicationId
     * @return
     */
    @PostMapping(value = "/detail/{applicationType}/{applicationId}")
    @ApiOperation("申请详情")
    public ApiResult<?> detail(@PathVariable(value = "applicationType") Integer applicationType, @PathVariable(value = "applicationId") Long applicationId) {
        if (applicationType == null || applicationId == null) {
            return ApiResult.newError("参数错误");
        }
        if (ApplicationTypeEnum.REGULAR.getCode().equals(applicationType) || ApplicationTypeEnum.EXTENSION_REGULAR.getCode().equals(applicationType)) {
            RegularApproveVO vo = regularApproveService.detail(applicationId);
            return vo != null ? ApiResult.newSuccess(vo) : ApiResult.newError("数据不存在");
        } else if (ApplicationTypeEnum.TRANSFER.getCode().equals(applicationType)) {
            TransferApproveVO vo = transferApproveService.detail(applicationId);
            return vo != null ? ApiResult.newSuccess(vo) : ApiResult.newError("数据不存在");
        } else if (ApplicationTypeEnum.RESIGN.getCode().equals(applicationType)) {
            ResignApproveVO vo = resignApproveService.detail(applicationId);
            return vo != null ? ApiResult.newSuccess(vo) : ApiResult.newError("数据不存在");
        }
        return ApiResult.newError("参数错误");
    }

    /**
     * 审批
     *
     * @param approvalProcessDTO
     * @return
     */
    @PostMapping(value = "/process")
    @ApiOperation("审批")
    public ApiResult<?> process(@RequestBody() ApprovalProcessDTO approvalProcessDTO) {
        log.info("RequestBody : {}", JSON.toJSONString(approvalProcessDTO));
        Asserts.isFalse(approvalProcessDTO == null || approvalProcessDTO.getId() == null, CErrorCode.REQUEST_PARAM_ERROR, "参数ID不能为空");
        Asserts.isFalse(approvalProcessDTO.getStatus() == null, CErrorCode.REQUEST_PARAM_ERROR, "审批结果不能为空");
        if (!Arrays.asList(ApproveStatusEnum.PASS.getCode(), ApproveStatusEnum.REJECT.getCode()).contains(approvalProcessDTO.getStatus())) {
            return ApiResult.newError("审批结果参数错误");
        }
        boolean result = approvalProcessService.process(approvalProcessDTO);
        return result ? ApiResult.newSuccess() : ApiResult.newError("操作失败");
    }

    /**
     * 批量审批通过
     *
     * @param approvalProcessDTO
     * @return
     */
    @PostMapping(value = "/batchPass")
    @ApiOperation("批量审批通过")
    public ApiResult<?> batchPass(@RequestBody() ApprovalProcessDTO approvalProcessDTO) {
        log.info("RequestBody : {}", JSON.toJSONString(approvalProcessDTO));
        Asserts.isFalse(approvalProcessDTO == null || CollectionUtils.isEmpty(approvalProcessDTO.getIdList()), CErrorCode.REQUEST_PARAM_ERROR, "参数ID不能为空");
        String mes = "";
        int success = 0;
        List<Long> idList = approvalProcessDTO.getIdList();
        for (Long id : idList) {
            ApprovalProcessDTO dto = new ApprovalProcessDTO();
            dto.setId(id);
            dto.setStatus(ApproveStatusEnum.PASS.getCode());
            try {
                boolean result = approvalProcessService.process(dto);
                if (result) {
                    success++;
                }
            } catch (BusinessException e) {
                mes += String.format("{id=%s ,失败信息: %s}", id, e.getMsg()) + ",";
                log.error(e.getMsg(), e);
            }
        }
        return success == idList.size() ? ApiResult.newSuccess() : ApiResult.newError(String.format("操作 %s 条,成功 %s 条，失败信息: %s", idList.size(), success, mes));
    }
}
