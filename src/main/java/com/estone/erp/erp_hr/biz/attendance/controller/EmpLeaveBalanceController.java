package com.estone.erp.erp_hr.biz.attendance.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.erp_hr.biz.attendance.constant.CompensatoryLeaveStatusEnum;
import com.estone.erp.erp_hr.biz.attendance.constant.LeavePlatformStatusEnum;
import com.estone.erp.erp_hr.biz.attendance.constant.LeaveRecordTypeEnum;
import com.estone.erp.erp_hr.biz.attendance.dto.LeaveBalanceQueryDto;
import com.estone.erp.erp_hr.biz.attendance.entity.EmpLeaveBalance;
import com.estone.erp.erp_hr.biz.attendance.entity.EmpLeaveTransactionRecords;
import com.estone.erp.erp_hr.biz.attendance.service.EmpLeaveBalanceService;
import com.estone.erp.erp_hr.biz.attendance.service.EmpLeaveTransactionRecordsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 员工假期额度总览表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@RestController
@RequestMapping("/empLeaveBalance")
@Slf4j
public class EmpLeaveBalanceController {
    @Resource
    private EmpLeaveBalanceService empLeaveBalanceService;

    @Resource
    private EmpLeaveTransactionRecordsService   empLeaveTransactionRecordsService;

    /**
     * 获取调休假期状态枚举列表
     * @return 调休假期状态枚举列表
     */
    @GetMapping("/compensatoryLeaveStatus")
    public ApiResult<Map<Integer, String>> getCompensatoryLeaveStatus() {
        Map<Integer, String> statusMap = Arrays.stream(CompensatoryLeaveStatusEnum.values())
            .collect(Collectors.toMap(CompensatoryLeaveStatusEnum::getCode, CompensatoryLeaveStatusEnum::getStatus));
        return ApiResult.newSuccess(statusMap);
    }

    /**
     * 同步所有在职用户的假期余额
     * @return 同步结果，包含成功或失败信息
     */
    @PostMapping("/syncLeaveBalance")
    public ApiResult<?> syncLeaveBalance(@RequestBody List<String> empNoList) {
        try {
            boolean result = empLeaveBalanceService.syncAllActiveUsersLeaveBalance(empNoList);
            if (result) {
                return ApiResult.newSuccess("假期余额同步成功");
            } else {
                return ApiResult.newError("假期余额同步失败");
            }
        } catch (Exception e) {
            return ApiResult.newError("假期余额同步异常: " + e.getMessage());
        }
    }

    /**
     * 同步所有在职用户的假期余额
     * @return 同步结果，包含成功或失败信息
     */
    @PostMapping("/syncEmpLeaveTransactionRecords")
    public ApiResult<?> syncEmpLeaveTransactionRecords(@RequestBody List<String> empNoList) {
        if (CollectionUtils.isEmpty(empNoList)) {
            return ApiResult.newError("员工编号列表不能为空");
        }
        if (empNoList.size() > 50) {
            return ApiResult.newError("员工编号列表长度不能超过50");
        }

        try {
            boolean result = empLeaveBalanceService.syncAllActiveUsersLeaveBalance(empNoList);
            log.info("同步员工假期余额结果：{}", result ? "成功" : "失败");
        } catch (Exception e) {
            log.error("同步员工假期余额异常：{}", e.getMessage());
        }
        try {
            boolean result2 = empLeaveBalanceService.syncAllEmpLeaveTransactionRecords(empNoList);
            log.info("同步员工假期余额变动结果：{}", result2? "成功" : "失败");
        } catch (Exception e) {
            log.error("同步员工假期余额变动异常：{}", e.getMessage());
        }
        try {
            boolean result3 = empLeaveBalanceService.calculateAndUpdateSurplusHours(empNoList);
            log.info("计算并更新剩余假期数量结果：{}", result3? "成功" : "失败");
        } catch (Exception e) {
            log.error("计算并更新剩余假期数量异常：{}", e.getMessage());
        }
        return ApiResult.newSuccess();
    }
    
    /**
     * 计算并更新剩余假期小时数
     * @param empNoList 员工编号列表
     * @return 处理结果
     */
    @PostMapping("/calculateAndUpdateSurplusHours")
    public ApiResult<?> calculateAndUpdateSurplusHours(@RequestBody List<String> empNoList) {
        try {
            boolean result = empLeaveBalanceService.calculateAndUpdateSurplusHours(empNoList);
            if (result) {
                return ApiResult.newSuccess("剩余假期小时数更新成功");
            } else {
                return ApiResult.newError("剩余假期小时数更新失败");
            }
        } catch (Exception e) {
            return ApiResult.newError("剩余假期小时数更新异常: " + e.getMessage());
        }
    }
    
    /**
     * 查询员工假期余额
     * @param queryParam 查询参数
     * @return 员工假期余额列表
     */
    @PostMapping("/list")
    public ApiResult<?> queryLeaveBalance(@RequestBody LeaveBalanceQueryDto queryParam) {
        try {
            if (StringUtils.isNotBlank(queryParam.getName()) && queryParam.getName().matches("^[a-zA-Z0-9]+$")) {
                queryParam.setEmployeeNo(queryParam.getName());
                queryParam.setName(null);
            }
            Page<EmpLeaveBalance> empLeaveBalancePage = empLeaveBalanceService.queryLeaveBalance(queryParam);
            return ApiResult.newSuccess(empLeaveBalancePage);
        } catch (Exception e) {
            log.error("查询假期余额异常: {}", e.getMessage());
            return ApiResult.newError("查询假期余额异常: " + e.getMessage());
        }
    }
    
    @PostMapping("/export")
    public void export(@RequestBody LeaveBalanceQueryDto queryParam, HttpServletResponse response) {
        if (queryParam==null) {
            queryParam = new LeaveBalanceQueryDto();
        }
        if (StringUtils.isNotBlank(queryParam.getName()) && queryParam.getName().matches("^[a-zA-Z0-9]+$")) {
            queryParam.setEmployeeNo(queryParam.getName());
            queryParam.setName(null);
        }
        if (CollectionUtils.isNotEmpty(queryParam.getIds())) {
            List<Integer> ids = queryParam.getIds();
            queryParam = new LeaveBalanceQueryDto();
            queryParam.setIds(ids);
        }
        queryParam.setPageRequired(false);
        empLeaveBalanceService.export(response, queryParam);

    }
    
    /**
     * 根据用户ID查询假期消费记录
     * @param userId 用户ID
     * @return 假期消费记录列表
     */
    @PostMapping("/getLeaveTransactionRecords")
    public ApiResult<?> getLeaveTransactionRecords(@RequestBody String userId) {
        try {
            if (StringUtils.isBlank(userId)) {
                return ApiResult.newError("用户ID不能为空");
            }
            LambdaQueryWrapper<EmpLeaveTransactionRecords> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                    // 主条件：userId必须匹配
                    .eq(EmpLeaveTransactionRecords::getUserId, userId)
                    // 嵌套条件组
                    .and(wrapper -> wrapper
                            .eq(EmpLeaveTransactionRecords::getLeaveRecordType, LeaveRecordTypeEnum.MODIFY_QUOTA.getCode())
                            .or()
                            .nested(subWrapper ->
                                    subWrapper.eq(EmpLeaveTransactionRecords::getLeaveRecordType, LeaveRecordTypeEnum.LEAVE.getCode())
                                            .eq(EmpLeaveTransactionRecords::getLeaveStatus, LeavePlatformStatusEnum.SUCCESS.getCode())
                            )
                    )
                    .orderByDesc(EmpLeaveTransactionRecords::getGmtCreate);
            List<EmpLeaveTransactionRecords> records = empLeaveTransactionRecordsService.list(queryWrapper);
            return ApiResult.newSuccess(records);
        } catch (Exception e) {
            return ApiResult.newError("查询假期消费记录异常: " + e.getMessage());
        }
    }
    
}

