package com.estone.erp.erp_hr.biz.attendance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.erp_hr.biz.attendance.dto.LeaveBalanceQueryDto;
import com.estone.erp.erp_hr.biz.attendance.entity.EmpLeaveAdjustmentLog;
import com.estone.erp.erp_hr.biz.attendance.mapper.EmpLeaveAdjustmentLogMapper;
import com.estone.erp.erp_hr.biz.attendance.service.EmpLeaveAdjustmentLogService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 假期额度调剂日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
@Service
public class EmpLeaveAdjustmentLogServiceImpl extends ServiceImpl<EmpLeaveAdjustmentLogMapper, EmpLeaveAdjustmentLog> implements EmpLeaveAdjustmentLogService {

    /**
     * 分页查询假期额度调剂日志
     * @param queryParam 查询参数对象
     * @return 分页结果
     */
    public Page<EmpLeaveAdjustmentLog> queryLeaveAdjustmentLog(LeaveBalanceQueryDto queryParam) {
        LambdaQueryWrapper<EmpLeaveAdjustmentLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmpLeaveAdjustmentLog::getAdjustType, 2)
                   .like(EmpLeaveAdjustmentLog::getRemark, "成功")
                   .orderByDesc(EmpLeaveAdjustmentLog::getLogTime);
        Page<EmpLeaveAdjustmentLog> page = new Page<>(queryParam.getCurrentPage(), queryParam.getPageSize());
        return this.page(page, queryWrapper);
    }
}
