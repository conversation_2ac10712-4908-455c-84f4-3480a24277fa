package com.estone.erp.erp_hr.biz.attendance.entity;

import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.estone.erp.erp_hr.biz.attendance.constant.CompensatoryLeaveStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 员工假期额度总览表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="EmpLeaveBalance对象", description="员工假期额度总览表")
public class EmpLeaveBalance implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @ExcelIgnore
    private Long id;

    @ApiModelProperty(value = "员工用户ID")
    @ExcelIgnore
    private String userId;

    @ExcelProperty(value = "员工姓名")
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    @ExcelProperty(value = "工号")
    @ApiModelProperty(value = "工号")
    private String employeeNo;

    @ExcelProperty(value = "部门")
    @ApiModelProperty(value = "部门")
    private String department;

    @ApiModelProperty(value = "假期类型唯一标识（如f84a2xxxx）")
    @ExcelIgnore
    private String leaveCode;

    @ApiModelProperty(value = "假期类型(1:年假 2:调休假 3:加班补休 4:其他)")
    @ExcelIgnore
    private Integer leaveType;

    @ApiModelProperty(value = "额度周期（如2019）")
    @ExcelIgnore
    private String quotaCycle;

    @ApiModelProperty(value = "配额唯一标记（如f8abb2xxxx）")
    @ExcelIgnore
    private String quotaId;

    @ApiModelProperty(value = "小时额度总数（1000=10小时）")
    @ExcelIgnore
    private Long quotaNumPerHour;

    @ApiModelProperty(value = "天额度总数（1000=10天）")
    @ExcelIgnore
    private Long quotaNumPerDay;

    @ApiModelProperty(value = "已用小时数（1000=10小时）")
    @ExcelIgnore
    private Long usedNumPerHour;

    @ApiModelProperty(value = "已用天数（100=1天）")
    @ExcelIgnore
    private Long usedNumPerDay;

    @ApiModelProperty(value = "有效期开始时间戳（毫秒）")
    @ExcelIgnore
    private Long startTime;

    @ApiModelProperty(value = "有效期结束时间戳（毫秒）")
    @ExcelIgnore
    private Long endTime;

    @ApiModelProperty(value = "计算后总余额(小时)")
    @ExcelProperty(value = "总余额(小时)")
    private BigDecimal totalHours;

    @ApiModelProperty(value = "计算最早到期日")
    @ExcelProperty(value = "最早到期日")
    private Date earliestExpiryDate;

    @ApiModelProperty(value = "状态(1:正常 2:即将过期 3:已过期)")
    @ExcelIgnore
    private Integer status;

    @ExcelProperty(value = "状态")
    @TableField(exist = false)
    private String statusName;

    @ApiModelProperty(value = "最后更新时间")
    @ExcelIgnore
    private Date lastUpdated;

    @ApiModelProperty(value = "起始计算时间")
    @ExcelIgnore
    private Long calcStartTime;

    public String getStatusName() {
        return status != null ?
              CompensatoryLeaveStatusEnum.getStatusByCode(status) : null;
    }

}
