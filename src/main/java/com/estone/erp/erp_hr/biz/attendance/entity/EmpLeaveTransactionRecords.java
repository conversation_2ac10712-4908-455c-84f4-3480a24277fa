package com.estone.erp.erp_hr.biz.attendance.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 假期消费记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="EmpLeaveTransactionRecords对象", description="假期消费记录表")
public class EmpLeaveTransactionRecords implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "假期消费记录唯一标识")
    private String recordId;

    @ApiModelProperty(value = "员工用户ID")
    private String userId;

    @ApiModelProperty(value = "假期类型标识")
    private String leaveCode;

    @ApiModelProperty(value = "关联的额度ID")
    private String quotaId;

    @ApiModelProperty(value = "计算类型(insert/add/delete/update/null)")
    private String calType;

    @ApiModelProperty(value = "记录类型(leave/update/modify_quota)")
    private String leaveRecordType;

    @ApiModelProperty(value = "交易类型(1:发放 2:使用 3:调整)")
    private Integer transactionType;

    @ApiModelProperty(value = "调整原因(1:系统校正 2:人工修改)")
    private Integer adjustReason;

    @ApiModelProperty(value = "请假状态(init/success/refuse/abort/revoke)")
    private String leaveStatus;

    @ApiModelProperty(value = "记录状态(1:正常 2:已撤销)")
    private Integer status;

    @ApiModelProperty(value = "开始时间戳（毫秒）")
    private Long startTime;

    @ApiModelProperty(value = "结束时间戳（毫秒）")
    private Long endTime;

    @ApiModelProperty(value = "创建时间戳（毫秒）")
    private Long gmtCreate;

    @ApiModelProperty(value = "修改时间戳（毫秒）")
    private Long gmtModified;

    @ApiModelProperty(value = "消费天数（100=1天）")
    private Long recordNumPerDay;

    @ApiModelProperty(value = "下发记录剩余小时数")
    private Long surplusNumPerHour;

    @ApiModelProperty(value = "消费小时数（100=1小时）")
    private Long recordNumPerHour;

    @ApiModelProperty(value = "显示单位")
    private String leaveViewUnit;

    @ApiModelProperty(value = "过期时间")
    private Date expireDate;

    @ApiModelProperty(value = "请假/调整原因")
    private String leaveReason;

    @ApiModelProperty(value = "操作人ID")
    private String opUserId;

    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;

    @ApiModelProperty(value = "标准化创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "标准化更新时间")
    private Date updatedAt;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "计算小时数（自动转换）")
    private BigDecimal calculatedHours;


    /**
     * 获取过期或剩余天数
     * @return 过期天数（负数）或剩余天数（正数）
     */
    public long getExpireDayStr() {
        if (expireDate == null) {
            return 0;
        }
        long diff = expireDate.getTime() - System.currentTimeMillis();
        return (long) Math.ceil(diff / (1000 * 60 * 60 * 24d));
    }

    /**
     * 获取过期状态和天数组合字符串
     * @return 状态字符串（已过期X天/剩余X天）
     */
    public String getExpireStatusWithDayStr() {
        if (expireDate == null) {
            return "";
        }
        long days = getExpireDayStr();
        if (days <= 0) {
            return "已过期" + Math.abs(days) + "天";
        } else {
            return "剩余" + days + "天";
        }
    }

    // 剩余小时数
    public String getSurplusNumPerHourStr () {
        if (surplusNumPerHour == null) {
            return "";
        }
        return String.format("%.1f", surplusNumPerHour/100.0);
    }

    // 发放
    public String getRecordNumPerHourStr () {
        if (recordNumPerHour == null) {
            return "";
        }
        return String.format("%.1f", recordNumPerHour/100.0);
    }
}
