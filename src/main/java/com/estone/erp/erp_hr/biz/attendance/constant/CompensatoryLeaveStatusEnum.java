package com.estone.erp.erp_hr.biz.attendance.constant;

/**
 * 调休假期状态枚举类
 */
public enum CompensatoryLeaveStatusEnum {
    NORMAL(1, "正常"),
    ABOUT_TO_EXPIRE(2, "即将过期"),
    EXPIRED(3, "已过期");

    private final int code;
    private final String status;

    CompensatoryLeaveStatusEnum(int code, String status) {
        this.code = code;
        this.status = status;
    }

    public int getCode() {
        return code;
    }

    public String getStatus() {
        return status;
    }

    public static String getStatusByCode(int code) {
        for (CompensatoryLeaveStatusEnum value : values()) {
            if (value.getCode() == code) {
                return value.getStatus();
            }
        }
        return null;
    }
}