package com.estone.erp.erp_hr.biz.attendance.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 假期额度调剂日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="EmpLeaveAdjustmentLog对象", description="假期额度调剂日志表")
public class EmpLeaveAdjustmentLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "操作时间(收款时间)")
    private Date logTime;

    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    @ApiModelProperty(value = "所属部门")
    private String department;

    @ApiModelProperty(value = "调剂批次号")
    private String batchNo;

    @ApiModelProperty(value = "调剂类型(1:清零 2:发送消息)")
    private Integer adjustType;

    @ApiModelProperty(value = "调剂小时数")
    private BigDecimal adjustHours;

    @ApiModelProperty(value = "处理详情(如:清零12小时)")
    private String adjustAction;

    @ApiModelProperty(value = "原发放日期")
    private Date originalIssueDate;

    @ApiModelProperty(value = "原发放小时数")
    private BigDecimal originalHours;

    @ApiModelProperty(value = "原到期日期")
    private Date originalExpiryDate;

    @ApiModelProperty(value = "调剂前剩余小时数")
    private BigDecimal beforeHours;

    @ApiModelProperty(value = "调剂后剩余小时数")
    private BigDecimal afterHours;

    @ApiModelProperty(value = "操作人ID")
    private String operatorId;

    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;

    @ApiModelProperty(value = "操作人类型(1:系统自动 2:管理员)")
    private Integer operatorType;

    @ApiModelProperty(value = "调剂原因")
    private String adjustReason;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
