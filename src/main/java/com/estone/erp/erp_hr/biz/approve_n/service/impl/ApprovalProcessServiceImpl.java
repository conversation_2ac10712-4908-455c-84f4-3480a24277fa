package com.estone.erp.erp_hr.biz.approve_n.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dingtalk.api.response.OapiProcessInstanceTerminateResponse;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.CErrorCode;
import com.estone.erp.common.mongo.model.CQuery;
import com.estone.erp.common.mongo.model.CQueryResult;
import com.estone.erp.common.redis.util.TokenUtils;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.erp_hr.biz.approve_n.dto.ApplyApprovalDTO;
import com.estone.erp.erp_hr.biz.approve_n.dto.ApprovalProcessDTO;
import com.estone.erp.erp_hr.biz.approve_n.dto.CanalApproveDTO;
import com.estone.erp.erp_hr.biz.approve_n.dto.ProcessContentDTO;
import com.estone.erp.erp_hr.biz.approve_n.entity.*;
import com.estone.erp.erp_hr.biz.approve_n.enums.ApplicationTypeEnum;
import com.estone.erp.erp_hr.biz.approve_n.enums.ApproveStatusEnum;
import com.estone.erp.erp_hr.biz.approve_n.enums.ProcessStepEnum;
import com.estone.erp.erp_hr.biz.approve_n.enums.ProcessTypeEnum;
import com.estone.erp.erp_hr.biz.approve_n.mapper.ApprovalProcessMapper;
import com.estone.erp.erp_hr.biz.approve_n.service.*;
import com.estone.erp.erp_hr.biz.attendance.controller.ApproveProcessUtils;
import com.estone.erp.erp_hr.biz.attendance.service.ReplenishWorkOvertimeService;
import com.estone.erp.erp_hr.biz.employee.entity.EmplEntity;
import com.estone.erp.erp_hr.biz.employee.enums.EmplStatusEnum;
import com.estone.erp.erp_hr.biz.employee.service.EmplService;
import com.estone.erp.erp_hr.biz.performance.dingdingApi.DingUtil;
import com.estone.erp.erp_hr.biz.performance.dingdingApi.dto.DingMsgDTO;
import com.estone.erp.erp_hr.biz.recruitment.service.RecruitmentNeedService;
import com.estone.erp.erp_hr.biz.sys.service.DeptService;
import com.estone.erp.erp_hr.biz.sys.vo.DeptVO;
import com.estone.erp.erp_hr.util.DataContextUtils;
import com.estone.erp.erp_hr.util.DingDingClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 审批流程
 *
 * @ClassName: ApprovalProcess
 * <AUTHOR>
 * @Date 2021-02-20 15:21:26
 * @Version: 0.0.1
 */
@Service("approvalProcessService")
@Slf4j
public class ApprovalProcessServiceImpl extends ServiceImpl<ApprovalProcessMapper, ApprovalProcessEntity> implements ApprovalProcessService {

    @Autowired
    private RegularApproveService regularApproveService;

    @Autowired
    private TransferApproveService transferApproveService;

    @Autowired
    private ResignApproveService resignApproveService;

    @Autowired
    private ReplenishWorkOvertimeService replenishWorkOvertimeService;

    @Autowired
    private EmplService emplService;

    @Autowired
    private DingUtil dingUtil;

    @Resource
    private DeptService deptService;

    @Autowired
    private RecruitmentNeedService recruitmentNeedService;

    @Autowired
    private AttendanceApproveService attendanceApproveService;

    @Resource
    private ApplyApprovalDetailService applyApprovalDetailService;

    @Resource
    private ApproveProcessUtils approveProcessUtils;

    @Resource
    private ApplyApprovalService applyApprovalService;

    @Resource
    private LeaveApproveService leaveApproveService;

    @Override
    public List<ApprovalProcessEntity> getListByProcessInstanceId(String processInstanceId) {
        List<ApprovalProcessEntity> list = this.list(Wrappers.<ApprovalProcessEntity>lambdaQuery().eq(ApprovalProcessEntity::getProcessInstanceId, processInstanceId));
        list.sort(new Comparator<ApprovalProcessEntity>() {
            @Override
            public int compare(ApprovalProcessEntity o1, ApprovalProcessEntity o2) {
                return o1.getOrderNumber().compareTo(o2.getOrderNumber());
            }
        });
        return list;
    }

    /**
     * 申请/审批--列表查询
     *
     * @param query
     * @return
     */
    @Override
    public CQueryResult<ApprovalProcessEntity> listApprovalProcess(CQuery<ApprovalProcessDTO> query) {
        // 分页结果
        CQueryResult<ApprovalProcessEntity> cQueryResult = new CQueryResult<>(0, 0, null);

        // 分页参数
        int offset = query.getOffset();
        int limit = query.getLimit();

        // 请求参数
        ApprovalProcessDTO approvalProcessDTO = query.getSearch();
        int total = baseMapper.countByDTO(approvalProcessDTO);
        if (total > 0) {
            if (total < limit) {
                limit = total;
            }
            List<ApprovalProcessEntity> rows = null;
            // 分页
            String orderByClause = query.getOrderByClause("a");
            if (StringUtils.isBlank(orderByClause)) {
                orderByClause = "a.id DESC";
            }
            // 不分页
            if (!query.isPageReqired()) {
                offset = 0;
                limit = total;
            }
            rows = baseMapper.listApprovalProcess(approvalProcessDTO, orderByClause, offset, limit);

            if (CollectionUtils.isNotEmpty(rows)) {
                cQueryResult.setRows(rows);
                cQueryResult.setTotal(total);
                cQueryResult.setTotalPages((total - 1) / limit + 1);
            }
        }
        return cQueryResult;
    }

    /**
     * 审批
     *
     * @param approvalProcessDTO
     * @return
     */
    @Override
    @Transactional(value = "erpHrDataTransactionManager", rollbackFor = BusinessException.class)
    public boolean process(ApprovalProcessDTO approvalProcessDTO) {
        // 当前步骤（获取属于当前审批人的审批记录，也就是当前审批的流程）
        ApprovalProcessEntity dBProcess = baseMapper.selectById(approvalProcessDTO.getId());
        Asserts.isFalse(dBProcess == null, CErrorCode.REQUEST_PARAM_ERROR, "数据不存在");
        Asserts.isTrue(approvalProcessDTO.getIsDing() || ApproveStatusEnum.WAITTING.getCode().equals(dBProcess.getStatus()), CErrorCode.REQUEST_PARAM_ERROR, "不是审批中不允许操作");
        // 钉钉回调且审批已经通过或拒绝直接拒绝
        if (approvalProcessDTO.getIsDing() && (ApproveStatusEnum.PASS.getCode().equals(dBProcess.getStatus())
                || ApproveStatusEnum.REJECT.getCode().equals(dBProcess.getStatus()))){
            return true;
        }
        //定时任务/钉钉回调不需要判断这个
        if (!approvalProcessDTO.getIsJob() && approvalProcessDTO.getIsDing() == false){
            Asserts.isTrue(dBProcess.getApprover().equals(DataContextUtils.getEmployeeNo()), CErrorCode.REQUEST_PARAM_ERROR, "不是本人不允许操作");
        }
        if (ApplicationTypeEnum.TRANSFER.getCode().equals(dBProcess.getApplicationType()) &&
                ProcessStepEnum.HR.getCode().equals(dBProcess.getProcessStep())) {
            Asserts.isFalse(approvalProcessDTO.getEffectiveDate() == null, CErrorCode.REQUEST_PARAM_ERROR, "确认调岗时间不能为空");
        }

        dBProcess.setStatus(approvalProcessDTO.getStatus());
        List<ProcessContentDTO> processContentDTOs = dBProcess.getProcessContentDTOs();
        if (StringUtils.isNotBlank(approvalProcessDTO.getProcessContent()) || StringUtils.isNotBlank(approvalProcessDTO.getUrl())) {
            processContentDTOs.add(new ProcessContentDTO(approvalProcessDTO.getProcessContent(), approvalProcessDTO.getUrl(), dBProcess.getApproverName(), new Date()));
            dBProcess.setProcessContent(JSONObject.toJSONString(processContentDTOs));
        }
        dBProcess.setProcessDate(new Date());
        //获取当前申请的所有审批记录
        List<ApprovalProcessEntity> processEntityList = list(new QueryWrapper<ApprovalProcessEntity>()
                .eq("application_id", dBProcess.getApplicationId()).eq("application_type", dBProcess.getApplicationType()));
        Asserts.isFalse(CollectionUtils.isEmpty(processEntityList), CErrorCode.REQUEST_PARAM_ERROR, "审批流数据缺失");
        processEntityList.sort(new Comparator<ApprovalProcessEntity>() {
            @Override
            public int compare(ApprovalProcessEntity o1, ApprovalProcessEntity o2) {
                return o1.getOrderNumber().compareTo(o2.getOrderNumber());
            }
        });

        List<ApprovalProcessEntity> updateList = new ArrayList<>();
        // 申请人步骤（我自己申请本身也会有一条记录，并且是第一条）
        ApprovalProcessEntity approval = null;
        // 下一步骤是或签
        List<ApprovalProcessEntity> nextProcessList = new ArrayList<>();
        for (ApprovalProcessEntity entity : processEntityList) {
            // 第一条
            if (entity.getOrderNumber().equals(1)) {
                approval = entity;
            }
            // 当前条是或签
            if (entity.getOrderNumber().equals(dBProcess.getOrderNumber()) && !entity.getId().equals(dBProcess.getId())) {
                ApprovalProcessEntity curOR = new ApprovalProcessEntity();
                curOR.setId(entity.getId());
                curOR.setStatus(approvalProcessDTO.getStatus());
                updateList.add(curOR);
            }
            //下一条存在或签
            if (entity.getOrderNumber().equals(dBProcess.getOrderNumber() + 1)) {
                nextProcessList.add(entity);
            }
        }

        //这一行属于遗留代码，不知道该不该删，不删的话会对审批补加班时长申请造成影响，删的话可能会影响其他审批，所以保留这行代码，在外面套一层判断
        if (!ApplicationTypeEnum.REPLENISH_WORK_OVERTIME.getCode().equals(approval.getApplicationType())) {
            approval.setApprover(dBProcess.getApprover());
        }
        updateList.add(dBProcess);
        updateList.add(approval);
        ApprovalProcessEntity nextProcessParam = null;
        if (CollectionUtils.isNotEmpty(nextProcessList)) {
            if (ApproveStatusEnum.PASS.getCode().equals(approvalProcessDTO.getStatus())) {
                approval.setStatus(ApproveStatusEnum.WAITTING.getCode());
                for(ApprovalProcessEntity nextProcess:nextProcessList){
                    nextProcess.setStatus(ApproveStatusEnum.WAITTING.getCode());
                }
                nextProcessParam = nextProcessList.get(0);
                approval.setApprover(nextProcessParam.getApprover());
            } else {
                approval.setStatus(dBProcess.getStatus());
            }
            updateList.addAll(nextProcessList);
        } else {
            //nextProcess为空，说明当前审批为最后一条，由于列表显示申请是根据审批流第一条显示，所以修改第一条审批流状态
            approval.setStatus(dBProcess.getStatus());
        }
        updateBatchById(updateList);
        if (ApplicationTypeEnum.REGULAR.getCode().equals(dBProcess.getApplicationType())
                || ApplicationTypeEnum.EXTENSION_REGULAR.getCode().equals(dBProcess.getApplicationType())
                || ApplicationTypeEnum.EXTENDED_PROBATION.getCode().equals(dBProcess.getApplicationType())) {
            // 转正审批、延期转正、延长试用期
            return regularApproveService.process(dBProcess, nextProcessParam,approvalProcessDTO);
        } else if (ApplicationTypeEnum.TRANSFER.getCode().equals(dBProcess.getApplicationType())) {
            // 调职审批
            dBProcess.setEffectiveDate(approvalProcessDTO.getEffectiveDate());
            return transferApproveService.process(dBProcess, nextProcessParam,approvalProcessDTO);
        } else if (ApplicationTypeEnum.RESIGN.getCode().equals(dBProcess.getApplicationType())) {
            // 离职审批
            return resignApproveService.process(dBProcess, nextProcessParam,approvalProcessDTO);
        } else if (ApplicationTypeEnum.REPLENISH_WORK_OVERTIME.getCode().equals(dBProcess.getApplicationType())) {
            //补加班时长审批
            return replenishWorkOvertimeService.process(dBProcess, nextProcessParam,approvalProcessDTO);
        }else if (ApplicationTypeEnum.RECRUITMENT_APPLICATION.getCode().equals(dBProcess.getApplicationType())){
            //招聘需求
            return recruitmentNeedService.lastProcess(dBProcess, nextProcessParam,approvalProcessDTO);
        }else if(ApplicationTypeEnum.OVERTIME_TRANSFER_APPLY.getCode().equals(dBProcess.getApplicationType())){
            //加班时长转换
            return attendanceApproveService.process(dBProcess,nextProcessParam,approvalProcessDTO);
        }else{
            return applyApprovalService.process(dBProcess,nextProcessParam,approvalProcessDTO);
        }
        //throw new BusinessException(CErrorCode.REQUEST_PARAM_ERROR, "操作失败");
    }

    /**
     * 取消申请
     * @param canalApproveDTO
     * @return
     */
    @Override
    public boolean cancel(CanalApproveDTO canalApproveDTO) {
        Long applicationId = canalApproveDTO.getId();
        Integer applicationType = canalApproveDTO.getApplicationType();
        if (applicationId == null || applicationType == null) {
            throw new BusinessException(CErrorCode.REQUEST_PARAM_ERROR, "参数错误");
        }
        List<ApprovalProcessEntity> processEntityList = baseMapper.selectList(new QueryWrapper<ApprovalProcessEntity>()
                .eq("application_id", applicationId).eq("application_type", applicationType));
        Asserts.isFalse(CollectionUtils.isEmpty(processEntityList), CErrorCode.REQUEST_PARAM_ERROR, "审批流数据缺失");
        List<ApprovalProcessEntity> updateProcessList = new ArrayList<>();
        //获取审批过的审批人工号，用于发送钉钉消息
        List<String> empNos = new ArrayList<>();
        for (ApprovalProcessEntity entity : processEntityList) {
            Integer status = entity.getStatus();
            //审批过就是 通过或者驳回 (目前只有转正申请、延期转正需要发送)
            if(ApplicationTypeEnum.REGULAR.getCode().equals(applicationType) && ApplicationTypeEnum.EXTENSION_REGULAR.getCode().equals(applicationType)
                    &&(ApproveStatusEnum.PASS.getCode().equals(status) || ApproveStatusEnum.REJECT.getCode().equals(status))){
                empNos.add(entity.getApprover());
            }
            if (ApproveStatusEnum.WAITTING.getCode().equals(status)) {
                ApprovalProcessEntity update = new ApprovalProcessEntity();
                update.setId(entity.getId());
                update.setStatus(ApproveStatusEnum.CANCEL.getCode());
                if (ProcessTypeEnum.APPROVE.getCode().equals(entity.getProcessType())) {
                    update.setStatus(Integer.valueOf(0));
                }
                updateProcessList.add(update);
            }
        }
        boolean result = updateBatchById(updateProcessList);
        //系统发起的撤销才请求钉钉撤销接口
        if (result && !canalApproveDTO.getSystemFlag() && StrUtil.isNotBlank(canalApproveDTO.getProcessInstanceId())){
            EmplEntity emplEntity = emplService.getByEmployeeNo(canalApproveDTO.getApproveNo());
            if (emplEntity != null && !EmplStatusEnum.isDimission(emplEntity.getStatus()) && emplEntity.getUserId() != null){
                canalApproveDTO.setOperatingUserid(emplEntity.getUserId());
                try {
                    OapiProcessInstanceTerminateResponse oapiProcessInstanceTerminateResponse = DingDingClientUtils.canalApprove(canalApproveDTO);
                    if (oapiProcessInstanceTerminateResponse.isSuccess()) {
                        log.info("取消申请调用钉钉撤销接口成功，实例id：{}",canalApproveDTO.getId());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("取消申请调用钉钉撤销接口失败：{}",e.getMessage());
                    throw new RuntimeException("取消转正申请调用钉钉撤销接口失败，请稍后重试：" + e.getMessage());
                }
            }
        }
        // 事务提交后发消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
              @Override
              public void afterCommit() {
                  //发送钉钉消息
                  if (result && CollUtil.isNotEmpty(empNos)){
                      String employeeNo = processEntityList.get(0).getApplicant();
                      String employeeName = TokenUtils.getEmployeeName(employeeNo);
                      EmplEntity emplEntity = emplService.getOne(Wrappers.<EmplEntity>lambdaQuery().eq(EmplEntity::getEmployeeNo, employeeNo));
                      if (StringUtils.isBlank(employeeName) && emplEntity != null){
                          employeeName = emplEntity.getName();
                      }
                      //查询所有部门，避免循环中多次查询
                      Map<Long, DeptVO> deptVOMap = deptService.selectDeptID2Map();

                      //组装需要发送消息的userId以及内容
                      String userIds = emplService.list(Wrappers.<EmplEntity>lambdaQuery().in(EmplEntity::getEmployeeNo, empNos))
                              .stream().map(item -> item.getUserId()).collect(Collectors.joining(","));
                      String formatDate = DateUtil.format(new Date(), "yyyy-MM-dd HH:ss:mm");
                      String ApplicationName = ApplicationTypeEnum.getNameByCode(applicationType);
                      String conent = "您好,"+employeeName+"于"+formatDate+"取消了"+ApplicationName;
                      DeptVO deptVO = deptVOMap.get(emplEntity.getDeptId());
                      if (deptVO != null && StrUtil.isNotBlank(deptVO.getFullName())){
                          conent = "您好,所属于【"+deptVO.getFullName()+"】部门的"+employeeName+"于"+formatDate+"取消了"+ApplicationName;
                      }
                      DingMsgDTO dingMsgDTO = new DingMsgDTO();
                      dingMsgDTO.setUserIds(userIds);
                      dingMsgDTO.setConent(conent);
                      dingUtil.sendDingMessage(dingMsgDTO);
                  }
              }
          }
        );
        return true;
    }

    /**
     * 修改部门主管后变更审批人
     *
     * @param oldLeader
     * @param newLeader
     * @return
     */
    @Override
    public boolean updateApprover(String oldLeader, String newLeader) {
        /**
         * 审批流步骤 2-部门组长审批 3-部门主管审批 4-调出部门组长审批 5-调出部门主管审批 6-调入部门组长审批 7-调入部门主管审批
         */
        List<ApprovalProcessEntity> processEntities = baseMapper.selectList(new QueryWrapper<ApprovalProcessEntity>()
                .eq("approver", oldLeader).in("status", Arrays.asList(0, ApproveStatusEnum.WAITTING.getCode()))
                .in("process_step", Arrays.asList(ProcessStepEnum.GROUP.getCode(), ProcessStepEnum.SUPERIOR.getCode(),
                        ProcessStepEnum.OUT_GROUP.getCode(), ProcessStepEnum.OUT_SUPERIOR.getCode(),
                        ProcessStepEnum.IN_GROUP.getCode(), ProcessStepEnum.IN_SUPERIOR.getCode())));
        if (CollectionUtils.isNotEmpty(processEntities)) {
            for (ApprovalProcessEntity entity : processEntities) {
                boolean updateApprover = false;
                if (ApplicationTypeEnum.REGULAR.getCode().equals(entity.getApplicationType()) || ApplicationTypeEnum.EXTENSION_REGULAR.getCode().equals(entity.getApplicationType())) {
                    RegularApproveEntity regularApprove = regularApproveService.getById(entity.getApplicationId());
                    if (regularApprove != null && ApproveStatusEnum.WAITTING.getCode().equals(regularApprove.getStatus()) && oldLeader.equals(regularApprove.getApprover())) {
                        regularApprove.setApprover(newLeader);
                        regularApproveService.updateById(regularApprove);
                        updateApprover = true;
                    }
                } else if (ApplicationTypeEnum.TRANSFER.getCode().equals(entity.getApplicationType())) {
                    TransferApproveEntity transferApprove = transferApproveService.getById(entity.getApplicationId());
                    if (transferApprove != null && ApproveStatusEnum.WAITTING.getCode().equals(transferApprove.getStatus()) && oldLeader.equals(transferApprove.getApprover())) {
                        transferApprove.setApprover(newLeader);
                        transferApproveService.updateById(transferApprove);
                        updateApprover = true;
                    }
                } else if (ApplicationTypeEnum.RESIGN.getCode().equals(entity.getApplicationType())) {
                    ResignApproveEntity resignApprove = resignApproveService.getById(entity.getApplicationId());
                    if (resignApprove != null && ApproveStatusEnum.WAITTING.getCode().equals(resignApprove.getStatus()) && oldLeader.equals(resignApprove.getApprover())) {
                        resignApprove.setApprover(newLeader);
                        resignApproveService.updateById(resignApprove);
                        updateApprover = true;
                    }
                }
                if (updateApprover) {
                    List<ApprovalProcessEntity> entities = baseMapper.selectList(new QueryWrapper<ApprovalProcessEntity>().eq("application_id", entity.getApplicationId())
                            .eq("application_type", entity.getApplicationType())
                            .eq("process_step", ProcessStepEnum.APPLICATION.getCode()));
                    if (CollectionUtils.isNotEmpty(entities)) {
                        ApprovalProcessEntity processEntity = entities.get(0);
                        processEntity.setApprover(newLeader);
                        baseMapper.updateById(processEntity);
                    }
                }
                entity.setApprover(newLeader);
                baseMapper.updateById(entity);
            }
        }
        return true;
    }

    @Override
    public ApprovalProcessEntity getFirstByApplicationId(Long applicationId) {
        LambdaQueryWrapper<ApprovalProcessEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApprovalProcessEntity::getApplicationId, applicationId)
                .orderByAsc(ApprovalProcessEntity::getOrderNumber);

        List<ApprovalProcessEntity> list = this.list(queryWrapper);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }

        return list.get(0);
    }

    @Override
    @Transactional(value = "erpHrDataTransactionManager", rollbackFor = BusinessException.class)
    public void create(ApplyApprovalDTO applyApprovalDTO, List<ApprovalProcessEntity> approvalProcessEntityList, ApplyApprovalDetail applyApprovalDetail) throws Exception {
        if(applyApprovalDetailService.save(applyApprovalDetail)) {
            StartProcessInstanceResponse response = approveProcessUtils.createApproveInstance(applyApprovalDTO);
            log.info("创建审批实例，param:"+ JSONObject.toJSONString(applyApprovalDTO)+",rsp:"+JSONObject.toJSONString(response));
            //钉钉返回创建的审批实例id
            String instanceId = response.getBody().getInstanceId();
            if (response == null||response.body == null||StringUtils.isBlank(instanceId)){
                throw new RuntimeException("创建实例失败，param:"+JSONObject.toJSONString(applyApprovalDTO));
            }
            //审批实例节点
            List<OapiProcessinstanceGetResponse.TaskTopVo> taskTopVoList = new ArrayList<>();
            //获取审批实例详情
            OapiProcessinstanceGetResponse response1 = DingDingClientUtils.getProcessDetails(instanceId);
            if (response1.isSuccess()) {
                OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance = response1.getProcessInstance();
                if (processInstance != null){
                    taskTopVoList.addAll(processInstance.getTasks());
                }
            }
            //获取到审批节点的人员，排除申请人
            List<EmplEntity> entityList = emplService.list(Wrappers.<EmplEntity>lambdaQuery()
                    .in(EmplEntity::getEmployeeNo, approvalProcessEntityList.stream()
                            .map(approvalProcessEntity -> approvalProcessEntity.getApprover()).collect(Collectors.toList())));

            //审批流根据当前审批人userId匹配审批实例详情获取对应的taskId
            approvalProcessEntityList.forEach(approvalProcessEntity -> {
                approvalProcessEntity.setApplicationId(applyApprovalDetail.getId());
                approvalProcessEntity.setProcessInstanceId(instanceId);
                //根据顺序加入钉钉审批节点
                String approverUserId = entityList.stream().filter(empl -> empl.getEmployeeNo().equals(approvalProcessEntity.getApprover())).findFirst().get().getUserId();
                approvalProcessEntity.setApproverUserId(approverUserId);
                //第一条不需要taskId
                if (StrUtil.isNotBlank(approverUserId) && approvalProcessEntity.getOrderNumber() != 1){
                    String taskId = taskTopVoList.stream().filter(task -> task.getUserid().equals(approverUserId)).map(task -> task.getTaskid()).findFirst().get();
                    approvalProcessEntity.setTaskId(taskId);
                }
            });

            OapiProcessinstanceGetResponse processDetails = DingDingClientUtils.getProcessDetails(response.body.instanceId);
            if(StringUtils.isBlank(processDetails.getErrmsg())) {
                try {
                    OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance = processDetails.getProcessInstance();
                    List<OapiProcessinstanceGetResponse.TaskTopVo> tasks = processInstance.getTasks();
                    for(int j = 0; j<approvalProcessEntityList.size(); j++){
                        ApprovalProcessEntity approvalProcessEntity = approvalProcessEntityList.get(j);
                        OapiProcessinstanceGetResponse.TaskTopVo taskTopVo = tasks.get(j);
                        approvalProcessEntity.setApproverUserId(taskTopVo.getUserid());
                        approvalProcessEntity.setTaskId(taskTopVo.getTaskid());
                    }
                } catch (Exception e) {
                    log.error("实例【"+response.body.instanceId+"】获取任务失败！failed:"+JSONObject.toJSONString(processDetails),e);
                }
            }else{
                log.error("实例【"+response.body.instanceId+"】获取任务失败！failed:"+processDetails.getErrmsg());
            }
            // 创建审批流
            saveBatch(approvalProcessEntityList);
            // OperationLogUtils.log(String.valueOf(entity.getId()), "新建离职申请(同步钉钉数据)", OperationTypeEnum.ADD, LogModuleEnum.RESIGN_APPROVE);
        }
    }

    public void dingProcess(){

    }
}