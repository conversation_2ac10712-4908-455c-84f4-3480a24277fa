package com.estone.erp.erp_hr.biz.approve_n.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.CErrorCode;
import com.estone.erp.common.redis.config.RedisClusterTemplate;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.erp_hr.biz.approve_n.entity.ApprovalProcessEntity;
import com.estone.erp.erp_hr.biz.approve_n.entity.AttendanceApprove;
import com.estone.erp.erp_hr.biz.approve_n.entity.LeaveApprove;
import com.estone.erp.erp_hr.biz.approve_n.entity.RegularApproveEntity;
import com.estone.erp.erp_hr.biz.approve_n.enums.*;
import com.estone.erp.erp_hr.biz.employee.entity.EmplEntity;
import com.estone.erp.erp_hr.biz.employee.enums.EmplStatusEnum;
import com.estone.erp.erp_hr.biz.employee.service.EmplService;
import com.estone.erp.erp_hr.biz.sys.enums.DeptTypeEnum;
import com.estone.erp.erp_hr.biz.sys.vo.DeptVO;
import com.estone.erp.erp_hr.biz.temp.OldRedisTemplate;
import com.estone.erp.erp_hr.common.constant.RedisKeyConstant;
import com.estone.erp.erp_hr.common.constant.UserConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 构造审批流工具类
 * @date 2022年07月21日 16:58
 */
public class BuildProcessUtil {

    private static RedisClusterTemplate redisClusterTemplate = SpringUtils.getBean(RedisClusterTemplate.class);

    /**
     *
     * @param employeeNo 申请人
     * @param applicationContent 申请内容
     * @param applicationType 申请类型
     * @param createTime 申请时间
     * @param approver 当前处理人
     * @param processInstanceId 审批实例id
     * @param createdDate 创建时间
     * @return
     */
    public static ApprovalProcessEntity buildApproveEntity(String employeeNo, String applicationContent,
            Integer applicationType, Date createTime, String approver, String processInstanceId, Date createdDate) {
        ApprovalProcessEntity entity = new ApprovalProcessEntity();
        // 申请人
        entity.setApplicant(employeeNo);
        // 申请内容
        entity.setApplicationContent(applicationContent);
        // 申请时间
        entity.setApplicationDate(Optional.ofNullable(createTime).orElse(new Date()));
        // 申请类型
        entity.setApplicationType(applicationType);
        // 当前处理人，第一条和最后一条都是申请人自己，抛弃最后一条，刚创建的当前处理人在task任务节点的第一个
        entity.setApprover(approver);
        // 审批实例id
        entity.setProcessInstanceId(processInstanceId);
        // 是否钉钉生成
        entity.setDingDing(true);
        // 审批流序号
        entity.setOrderNumber(1);
        entity.setProcessStep(ProcessStepEnum.APPLICATION.getCode());
        // 流程类型 1-申请 2-审批
        entity.setProcessType(ProcessTypeEnum.APPLICATION.getCode());
        // 状态 默认0 1-审批中 2-已通过 3-已驳回 4-已取消
        entity.setStatus(ApproveStatusEnum.WAITTING.getCode());
        entity.setCreatedDate(createdDate);
        return entity;
    }

    /**
     * 创建第一个节点（转正申请）
     * @param regularApproveentity
     * @return com.estone.erp.erp_hr.biz.approve_n.entity.ApprovalProcessEntity
     * <AUTHOR>
     * @date 2021/9/28 17:53
     */
    public static ApprovalProcessEntity buildApprovalProcessEntity0(RegularApproveEntity regularApproveentity) {
        ApprovalProcessEntity entity0 = new ApprovalProcessEntity();
        // 申请人
        entity0.setApplicant(regularApproveentity.getEmployeeNo());
        // 申请内容
        entity0.setApplicationContent(ApplicationTypeEnum.getNameByCode(regularApproveentity.getApplicationType()));
        // 申请时间
        entity0.setApplicationDate(regularApproveentity.getApprovePositiveDate());
        // 申请关联ID
        //entity.setApplicationId();
        // 申请类型  1-转正申请 2-延期转正申请 3-调职申请 4-离职申请
        entity0.setApplicationType(regularApproveentity.getApplicationType());
        // 当前处理人 获取直系领导
        //entity0.setApprover(deptVO.getLeader());
        // 审批流序号
        entity0.setOrderNumber(1);
        //entity.setProcessContent();// 处理备注
        //entity.setProcessDate();// 处理时间
        /**
         * 审批流步骤 1-提交申请 2-部门组长审批 3-部门主管审批 4-调出部门组长审批 5-调出部门主管审批
         * 6-调入部门组长审批 7-调入部门主管审批 8-人事确认调职时间 9-工作交接人确认 10-考勤专员确认 11- 人事经理审批
         */
        entity0.setProcessStep(ProcessStepEnum.APPLICATION.getCode());
        // 流程类型 1-申请 2-审批
        entity0.setProcessType(ProcessTypeEnum.APPLICATION.getCode());
        // 状态 默认0 1-审批中 2-已通过 3-已驳回 4-已取消
        entity0.setStatus(ApproveStatusEnum.WAITTING.getCode());
        return entity0;
    }

    public static ApprovalProcessEntity buildAttendanceApproveEntity0(AttendanceApprove attendanceApprove) {
        ApprovalProcessEntity entity0 = new ApprovalProcessEntity();
        // 申请人
        entity0.setApplicant(attendanceApprove.getEmployeeNo());
        // 申请内容
        entity0.setApplicationContent(AttendanceApproveEnum.getNameByCode(attendanceApprove.getType()));
        // 申请时间
        entity0.setApplicationDate(new Date());
        // 申请类型
        entity0.setApplicationType(ApplicationTypeEnum.OVERTIME_TRANSFER_APPLY.getCode());
        // 审批流序号
        entity0.setOrderNumber(1);
        entity0.setProcessStep(ProcessStepEnum.APPLICATION.getCode());
        // 流程类型 1-申请 2-审批
        entity0.setProcessType(ProcessTypeEnum.APPLICATION.getCode());
        // 状态 默认0 1-审批中 2-已通过 3-已驳回 4-已取消
        entity0.setStatus(ApproveStatusEnum.WAITTING.getCode());
        return entity0;
    }

    /**
     * @Description :考勤专员
     * @Date 2021/12/20 17:03
     * @param: approvalList
     * @return java.util.List<com.estone.erp.erp_hr.biz.approve_n.entity.ApprovalProcessEntity>
     **/
    public static List<ApprovalProcessEntity> buildAttendanceApprove(List<ApprovalProcessEntity> approvalList,EmplEntity emplEntity) {

        //获取上一步,如果上一步是申请那么不要换order顺序
        ApprovalProcessEntity lastAp = approvalList.get(approvalList.size() - 1);

        ApprovalProcessEntity entity = new ApprovalProcessEntity();

        BeanUtil.copyProperties(lastAp, entity);

        //考勤专员
        String attHr = UserConstant.OXY_EMPL_NO;

        entity.setProcessStep(ProcessStepEnum.ATTENDANCE.getCode());
        if (emplEntity != null){
            attHr = emplEntity.getEmployeeNo();
            entity.setProcessStep(ProcessStepEnum.PERFORMANCE.getCode());
        }

        entity.setApprover(attHr);

        Integer orderNumber = lastAp.getOrderNumber();
        if (orderNumber != 1){
            entity.setOrderNumber(orderNumber + 1);
        }else {
            entity.setOrderNumber(2);
        }

        //如果是第二个则覆盖第一条当前处理人
        if (orderNumber == 1){
            approvalList.get(0).setApprover(attHr);
            entity.setStatus(1);
        }else {
            entity.setStatus(0);
        }

        entity.setProcessType(ProcessTypeEnum.APPROVE.getCode());
        entity.setApplicationId(lastAp.getApplicationId());

        approvalList.add(entity);

        return approvalList;
    }

    /**
     * @Description 创建亚马逊主管（王家
     * @Date 2022/8/17 17:31
     * @param: approvalList
     * @return java.util.List<com.estone.erp.erp_hr.biz.approve_n.entity.ApprovalProcessEntity>
     **/
    public static List<ApprovalProcessEntity> buiAdamazonLeaderApproval(List<ApprovalProcessEntity> approvalList) {

        //获取上一步,如果上一步是申请那么不要换order顺序
        ApprovalProcessEntity lastAp = approvalList.get(approvalList.size() - 1);

        ApprovalProcessEntity entity = new ApprovalProcessEntity();

        BeanUtil.copyProperties(lastAp, entity);

        //亚马逊组长 王家
        String performanceLeader = UserConstant.ADAMAZON_LEADER;

        entity.setApprover(performanceLeader);
        entity.setProcessStep(ProcessStepEnum.AMAZON_LEADER.getCode());
        Integer orderNumber = lastAp.getOrderNumber();
        if (orderNumber != 1){
            entity.setOrderNumber(orderNumber);
            //有BP的放部门负责人前审批
            lastAp.setOrderNumber(orderNumber + 1);
            lastAp.setStatus(0);
        }else {
            entity.setOrderNumber(2);
        }

        //如果是第二个则覆盖第一条当前处理人
        if (orderNumber == 1){
            approvalList.get(0).setApprover(performanceLeader);
            entity.setStatus(1);
        }else {
            entity.setStatus(0);
        }

        entity.setProcessType(ProcessTypeEnum.APPROVE.getCode());
        entity.setApplicationId(lastAp.getApplicationId());

        approvalList.add(entity);

        return approvalList;
    }

    /**
     * 创建薪酬绩效组长审批（孙思文）
     * @param approvalList
     * @return java.util.List<com.estone.erp.erp_hr.biz.approve_n.entity.ApprovalProcessEntity>
     * <AUTHOR>
     * @date 2021/9/28 17:24
     */
    public static List<ApprovalProcessEntity> buildPerformanceLeaderApproval(List<ApprovalProcessEntity> approvalList) {
        //获取上一步
        ApprovalProcessEntity lastAp = approvalList.get(approvalList.size() - 1);

        ApprovalProcessEntity entity = new ApprovalProcessEntity();

        BeanUtil.copyProperties(lastAp, entity);

        //薪酬绩效组长 孙思文
        String performanceLeader = UserConstant.PERFORMANCE_LEADER_NO;
        //String performanceLeader = PerformanceRuleConfig.PERFORMANCE_LEADER;
        Asserts.isFalse(StringUtils.isEmpty(performanceLeader), CErrorCode.REQUEST_PARAM_ERROR, "没有配置薪酬绩效组长-审批人账号");

        entity.setApprover(performanceLeader);
        entity.setProcessStep(ProcessStepEnum.PERFORMANCE_LEADER.getCode());
        entity.setOrderNumber(lastAp.getOrderNumber() + 1);

        entity.setProcessType(ProcessTypeEnum.APPROVE.getCode());
        entity.setStatus(ApproveStatusEnum.DEFAULT.getCode());
        entity.setApplicationId(lastAp.getApplicationId());

        approvalList.add(entity);

        return approvalList;
    }


    /**
     * 创建人事审批（欧阳金风）
     * @param approvalList
     * @return java.util.List<com.estone.erp.erp_hr.biz.approve_n.entity.ApprovalProcessEntity>
     * <AUTHOR>
     * @date 2021/9/28 17:22
     */
    public static List<ApprovalProcessEntity> buildPersonnelApproval(List<ApprovalProcessEntity> approvalList) {

        //获取上一步
        ApprovalProcessEntity lastAp = approvalList.get(approvalList.size() - 1);

        ApprovalProcessEntity entity = new ApprovalProcessEntity();

        BeanUtil.copyProperties(lastAp, entity);

        //欧阳金凤
        String hrLeader = UserConstant.HR_LEADER_NO;
        //String hrLeader = redisClusterTemplate.get(RedisKeyConstant.HR_APPROVE_PROCESS_HR_LEADER_EMPL);
        Asserts.isFalse(StringUtils.isEmpty(hrLeader), CErrorCode.REQUEST_PARAM_ERROR, "没有配置人事经理-审批人账号");

        entity.setApprover(hrLeader);
        entity.setProcessStep(ProcessStepEnum.HR_LEADER.getCode());
        entity.setOrderNumber(lastAp.getOrderNumber() + 1);

        entity.setProcessType(ProcessTypeEnum.APPROVE.getCode());
        entity.setStatus(ApproveStatusEnum.DEFAULT.getCode());
        entity.setApplicationId(lastAp.getApplicationId());

        approvalList.add(entity);

        return approvalList;
    }


    /**
     * @Description  创建周总审批（欧阳金风）
     * @Date 2022/7/21 16:24
     * @param: approvalList
     * @param: maxOrderNumber
     * @return java.util.List<com.estone.erp.erp_hr.biz.approve_n.entity.ApprovalProcessEntity>
     **/
    public static List<ApprovalProcessEntity> buildHRApprovalProcessEntity(List<ApprovalProcessEntity> approvalList, int maxOrderNumber) {
        ApprovalProcessEntity entity0 = approvalList.get(0);
        // 周总
        String zhouZong = UserConstant.ZHOU_ZONG;
        ApprovalProcessEntity process3 = new ApprovalProcessEntity();
        process3.setApprover(zhouZong);
        //第一条记录的处理人为下一条的处理人
        String approver3 = entity0.getApprover();
        if (StrUtil.isBlank(approver3)){
            entity0.setApprover(zhouZong);
        }
        process3.setProcessStep(ProcessStepEnum.ZHOU_ZONG.getCode());
        process3.setOrderNumber(2);
        // 流程类型 1-申请 2-审批
        process3.setProcessType(ProcessTypeEnum.APPROVE.getCode());
        process3.setStatus(ApproveStatusEnum.WAITTING.getCode());
        process3.setApplicant(entity0.getApplicant());
        process3.setApplicationContent(entity0.getApplicationContent());
        process3.setApplicationDate(entity0.getApplicationDate());
        process3.setApplicationType(entity0.getApplicationType());

        approvalList.add(process3);
        return approvalList;
    }

    /**
     * 组装-审批流数据
     * 
     * @param deptVOMap
     * @param deptId
     * @param employeeNo
     * @param entity0
     * @param ccUserIds
     * @return
     */
    public static List<ApprovalProcessEntity> buildApprovalProcess(Map<Long, DeptVO> deptVOMap, Long deptId, String employeeNo,
            ApprovalProcessEntity entity0, List<String> ccUserIds) {
        List<ApprovalProcessEntity> approvalList = new ArrayList<>();
        DeptVO olDdeptVO = deptVOMap.get(deptId);
        boolean isDept = false;
        int maxOrderNumber = 1;
        // 组装第一条申请数据
        approvalList.add(entity0);

        //是否循环内第一次新增记录
        boolean flagOne = true;
        // 组装调出审批流数据
        for (int i = 2; i <= 99; i++) {
            if (isDept || DeptTypeEnum.COMPANY.getCode().equals(olDdeptVO.getType()) || olDdeptVO == null) {
                break;
            }
            Asserts.isFalse(olDdeptVO.getType() == null, CErrorCode.REQUEST_PARAM_ERROR, "组织架构：" + olDdeptVO.getName() + " 没有指定部门类型");
            // 部门类型 1:公司 2：部门 3：组
            if (DeptTypeEnum.DEPT.getCode().equals(olDdeptVO.getType())) {
                isDept = true;
                Asserts.isFalse(StringUtils.isBlank(olDdeptVO.getLeader()), CErrorCode.REQUEST_PARAM_ERROR, "组织架构：" + olDdeptVO.getName() + " 没有指定主管");
            }
            if (StringUtils.isBlank(olDdeptVO.getLeader()) || StringUtils.equals(employeeNo,olDdeptVO.getLeader())) {
                // 部门领导是自己也不用审批
                olDdeptVO = deptVOMap.get(olDdeptVO.getParentId());
                continue;
            }
            ApprovalProcessEntity entity = new ApprovalProcessEntity();

            entity.setApplicant(entity0.getApplicant());
            entity.setApplicationContent(entity0.getApplicationContent());
            entity.setApplicationDate(entity0.getApplicationDate());
            entity.setApplicationType(entity0.getApplicationType());
            // 当前处理人
            entity.setApprover(olDdeptVO.getLeader());
            // 审批流序号
            entity.setOrderNumber(i);
            maxOrderNumber = i;
            /**
             * 审批流步骤 1-提交申请 2-部门组长审批 3-部门主管审批 4-调出部门组长审批 5-调出部门主管审批
             * 6-调入部门组长审批 7-调入部门主管审批 8-人事确认调职时间 9-工作交接人确认 10-考勤专员确认 11- 人事经理审批
             */
            entity.setProcessStep(DeptTypeEnum.DEPT.getCode().equals(olDdeptVO.getType()) ? ProcessStepEnum.SUPERIOR.getCode() : ProcessStepEnum.GROUP.getCode());
            // 流程类型 1-申请 2-审批
            entity.setProcessType(ProcessTypeEnum.APPROVE.getCode());
            entity.setStatus(0);
            if (flagOne) {
                entity0.setApprover(olDdeptVO.getLeader());
                entity.setStatus(ApproveStatusEnum.WAITTING.getCode());
            }
            approvalList.add(entity);
            flagOne = false;
            olDdeptVO = deptVOMap.get(olDdeptVO.getParentId());
        }
        Asserts.isFalse(CollectionUtils.isEmpty(approvalList), CErrorCode.REQUEST_PARAM_ERROR, "组织架构：" + olDdeptVO.getName() + " 无法创建审批流");

        // 组装人事审批流程
        List<ApprovalProcessEntity> hrProcessList = buildHRApprovalProcessEntity(entity0, maxOrderNumber);
        approvalList.addAll(hrProcessList);
        return approvalList;
    }

    /**
     * 组装人事审批数据
     *
     * @param entity0
     * @param maxOrderNumber
     * @return
     */
    public static List<ApprovalProcessEntity> buildHRApprovalProcessEntity(ApprovalProcessEntity entity0, int maxOrderNumber) {
        ApprovalProcessEntity process = new ApprovalProcessEntity();
        process.setApplicant(entity0.getApplicant());
        process.setApplicationContent(entity0.getApplicationContent());
        process.setApplicationDate(entity0.getApplicationDate());
        process.setApplicationType(entity0.getApplicationType());
        String hrApprover = redisClusterTemplate.get(RedisKeyConstant.HR_APPROVE_PROCESS_HR_EMPL);
        Asserts.isFalse(StringUtils.isEmpty(hrApprover), CErrorCode.REQUEST_PARAM_ERROR, "没有配置考勤专员确认-审批人账号");
        process.setApprover(hrApprover);
        // 审批流序号
        process.setOrderNumber(maxOrderNumber + 1);
        /**
         * 审批流步骤  10-考勤专员确认
         */
        process.setProcessStep(ProcessStepEnum.ATTENDANCE.getCode());
        // 流程类型 1-申请 2-审批
        process.setProcessType(ProcessTypeEnum.APPROVE.getCode());
        process.setStatus(0);

        ApprovalProcessEntity process2 = new ApprovalProcessEntity();
        BeanUtils.copyProperties(process, process2);
        // 人事经理 TODO 欧阳金凤 3324
        String hrLeader = UserConstant.HR_LEADER_NO;
        Asserts.isFalse(StringUtils.isEmpty(hrLeader), CErrorCode.REQUEST_PARAM_ERROR, "没有配置人事经理-审批人账号");
        process2.setApprover(hrLeader);
        process2.setProcessStep(ProcessStepEnum.HR_LEADER.getCode());
        process2.setOrderNumber(process.getOrderNumber() + 1);
        return Arrays.asList(process, process2);
    }
}
