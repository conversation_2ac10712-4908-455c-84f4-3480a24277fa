package com.estone.erp.erp_hr.biz.performance.dingdingApi;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.estone.erp.erp_hr.biz.dingding.service.DingdingService;
import com.estone.erp.erp_hr.biz.performance.dingdingApi.dto.DingMsgDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description TODO
 * @date 2021年11月05日 9:51
 */
@Slf4j
@Component
public class DingUtil {

    /** 钉钉发送消息api */
    private final static  String MSG_URL = "https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2";

    private final static Long AGENT_ID = 816834957L;

    @Autowired
    private DingdingService dingdingService;

    @Value("${spring.profiles.active}")
    private String active;

    /** 发送钉钉消息 给同一员工一天只能发送一条内容相同的消息通知。
     * 具体限制查看https://open.dingtalk.com/document/orgapp-server/gets-the-result-of-sending-messages-asynchronously-to-the-enterprise*/
    public OapiMessageCorpconversationAsyncsendV2Response sendDingMessage(DingMsgDTO dingMsgDTO){
        OapiMessageCorpconversationAsyncsendV2Response response = null;
        try{
            if (true) {
                String accessToken = dingdingService.syncToken();

                DingTalkClient client = new DefaultDingTalkClient(MSG_URL);
                OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
                //传入的是员工的工号
                request.setUseridList(dingMsgDTO.getUserIds());
                //申请的应用AgentId
                request.setAgentId(AGENT_ID);
                //true为全体员工
                request.setToAllUser(false);

                OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
                msg.setMsgtype("text");
                msg.setText(new OapiMessageCorpconversationAsyncsendV2Request.Text());
                msg.getText().setContent(dingMsgDTO.getConent());
                request.setMsg(msg);
                response = client.execute(request, accessToken);
            }else {
                log.warn("非生产环境不发送钉钉消息~");
                response = new OapiMessageCorpconversationAsyncsendV2Response();
                response.setErrcode(500L);
                response.setErrmsg("非生产环境不发送钉钉消息~");
            }
        }catch (Exception e){
            log.error("发送钉钉消息失败~，{}",e.getMessage());
        }
        return response;
    }


}
